"""
异步任务队列
基于 Celery 实现后台任务处理
"""
import asyncio
from typing import Optional, Any
from celery import Celery
from celery.result import AsyncResult
from app.config.settings import settings
from app.core.logging import get_logger

logger = get_logger(__name__)

# 创建 Celery 应用
celery_app = Celery(
    "ocr_tasks",
    broker=settings.REDIS_URL or "redis://localhost:6379/0",
    backend=settings.REDIS_URL or "redis://localhost:6379/0"
)

# Celery 配置
celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    task_track_started=True,
    task_time_limit=300,  # 5分钟超时
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
)

@celery_app.task(bind=True, name="process_ocr_task")
def process_ocr_async(self, image_data: bytes, task_id: str, company_id: Optional[int] = None):
    """
    异步OCR处理任务
    
    Args:
        image_data: 图片二进制数据
        task_id: 任务ID
        company_id: 公司ID（可选）
    
    Returns:
        dict: 处理结果
    """
    try:
        from PIL import Image
        import io
        from app.utils.ocr_processor import perform_ocr_recognition_sync
        from app.utils.company_matcher import match_company_sync
        
        # 更新任务状态
        self.update_state(state='PROCESSING', meta={'progress': 10})
        
        # 转换图片数据
        image = Image.open(io.BytesIO(image_data))
        self.update_state(state='PROCESSING', meta={'progress': 30})
        
        # 执行OCR识别
        ocr_result = perform_ocr_recognition_sync(image)
        self.update_state(state='PROCESSING', meta={'progress': 70})
        
        # 公司匹配（如果需要）
        company_match_result = None
        if company_id:
            company_match_result = match_company_sync(ocr_result, company_id)
        
        self.update_state(state='PROCESSING', meta={'progress': 90})
        
        result = {
            'task_id': task_id,
            'ocr_result': ocr_result,
            'company_match': company_match_result,
            'status': 'completed'
        }
        
        logger.info(f"OCR任务完成: {task_id}")
        return result
        
    except Exception as exc:
        logger.error(f"OCR任务失败 {task_id}: {exc}")
        self.update_state(
            state='FAILURE',
            meta={'error': str(exc), 'task_id': task_id}
        )
        raise


class TaskManager:
    """任务管理器"""
    
    @staticmethod
    def submit_ocr_task(image_data: bytes, task_id: str, company_id: Optional[int] = None) -> str:
        """
        提交OCR任务
        
        Args:
            image_data: 图片数据
            task_id: 任务ID
            company_id: 公司ID
            
        Returns:
            str: Celery任务ID
        """
        task = process_ocr_async.delay(image_data, task_id, company_id)
        logger.info(f"OCR任务已提交: {task_id}, Celery ID: {task.id}")
        return task.id
    
    @staticmethod
    def get_task_status(celery_task_id: str) -> dict:
        """
        获取任务状态
        
        Args:
            celery_task_id: Celery任务ID
            
        Returns:
            dict: 任务状态信息
        """
        result = AsyncResult(celery_task_id, app=celery_app)
        
        return {
            'task_id': celery_task_id,
            'status': result.status,
            'result': result.result if result.ready() else None,
            'info': result.info
        }
    
    @staticmethod
    def cancel_task(celery_task_id: str) -> bool:
        """
        取消任务
        
        Args:
            celery_task_id: Celery任务ID
            
        Returns:
            bool: 是否成功取消
        """
        try:
            celery_app.control.revoke(celery_task_id, terminate=True)
            logger.info(f"任务已取消: {celery_task_id}")
            return True
        except Exception as e:
            logger.error(f"取消任务失败 {celery_task_id}: {e}")
            return False


# 全局任务管理器实例
task_manager = TaskManager()