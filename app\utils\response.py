"""
响应工具函数
提供便捷的函数来生成统一格式的API响应
"""
from typing import Any, Optional, TypeVar, List
from fastapi import HTTPException
from fastapi.responses import JSONResponse

from ..schemas.response import (
    ApiResponse, 
    StatusCode, 
    ResponseMessage,
    PaginatedResponse
)
from ..schemas.pagination import PaginatedData, create_paginated_data

T = TypeVar('T')


def success_response(
    data: Any = None,
    message: str = ResponseMessage.SUCCESS,
    code: int = StatusCode.SUCCESS
) -> ApiResponse[Any]:
    """
    创建成功响应
    
    Args:
        data: 响应数据
        message: 响应消息
        code: 状态码
        
    Returns:
        ApiResponse: 统一格式的成功响应
    """
    return ApiResponse(
        code=code,
        message=message,
        data=data
    )


def error_response(
    message: str = ResponseMessage.INTERNAL_ERROR,
    code: int = StatusCode.INTERNAL_SERVER_ERROR,
    data: Any = None
) -> ApiResponse[Any]:
    """
    创建错误响应
    
    Args:
        message: 错误消息
        code: 错误状态码
        data: 错误数据（可选）
        
    Returns:
        ApiResponse: 统一格式的错误响应
    """
    return ApiResponse(
        code=code,
        message=message,
        data=data
    )


def paginated_response(
    items: List[Any],
    page: int,
    size: int,
    total: int,
    message: str = ResponseMessage.SUCCESS,
    code: int = StatusCode.SUCCESS
) -> ApiResponse[PaginatedData]:
    """
    创建分页响应
    
    Args:
        items: 数据列表
        page: 当前页码
        size: 每页大小
        total: 总记录数
        message: 响应消息
        code: 状态码
        
    Returns:
        ApiResponse: 包含分页数据的统一响应
    """
    paginated_data = create_paginated_data(items, page, size, total)
    
    return ApiResponse(
        code=code,
        message=message,
        data=paginated_data
    )


def created_response(
    data: Any = None,
    message: str = ResponseMessage.CREATED
) -> ApiResponse[Any]:
    """
    创建资源创建成功响应
    
    Args:
        data: 创建的资源数据
        message: 响应消息
        
    Returns:
        ApiResponse: 创建成功响应
    """
    return success_response(
        data=data,
        message=message,
        code=StatusCode.CREATED
    )


def updated_response(
    data: Any = None,
    message: str = ResponseMessage.UPDATED
) -> ApiResponse[Any]:
    """
    创建资源更新成功响应
    
    Args:
        data: 更新后的资源数据
        message: 响应消息
        
    Returns:
        ApiResponse: 更新成功响应
    """
    return success_response(
        data=data,
        message=message,
        code=StatusCode.SUCCESS
    )


def deleted_response(
    message: str = ResponseMessage.DELETED
) -> ApiResponse[None]:
    """
    创建资源删除成功响应
    
    Args:
        message: 响应消息
        
    Returns:
        ApiResponse: 删除成功响应
    """
    return success_response(
        data=None,
        message=message,
        code=StatusCode.SUCCESS
    )


def not_found_response(
    message: str = ResponseMessage.NOT_FOUND
) -> ApiResponse[None]:
    """
    创建资源不存在响应
    
    Args:
        message: 错误消息
        
    Returns:
        ApiResponse: 资源不存在响应
    """
    return error_response(
        message=message,
        code=StatusCode.NOT_FOUND
    )


def bad_request_response(
    message: str = ResponseMessage.BAD_REQUEST,
    data: Any = None
) -> ApiResponse[Any]:
    """
    创建请求参数错误响应
    
    Args:
        message: 错误消息
        data: 错误详情数据
        
    Returns:
        ApiResponse: 请求参数错误响应
    """
    return error_response(
        message=message,
        code=StatusCode.BAD_REQUEST,
        data=data
    )


def validation_error_response(
    message: str = ResponseMessage.VALIDATION_ERROR,
    data: Any = None
) -> ApiResponse[Any]:
    """
    创建数据验证错误响应
    
    Args:
        message: 错误消息
        data: 验证错误详情
        
    Returns:
        ApiResponse: 数据验证错误响应
    """
    return error_response(
        message=message,
        code=StatusCode.VALIDATION_ERROR,
        data=data
    )


def unauthorized_response(
    message: str = ResponseMessage.UNAUTHORIZED
) -> ApiResponse[None]:
    """
    创建未授权响应
    
    Args:
        message: 错误消息
        
    Returns:
        ApiResponse: 未授权响应
    """
    return error_response(
        message=message,
        code=StatusCode.UNAUTHORIZED
    )


def forbidden_response(
    message: str = ResponseMessage.FORBIDDEN
) -> ApiResponse[None]:
    """
    创建禁止访问响应
    
    Args:
        message: 错误消息
        
    Returns:
        ApiResponse: 禁止访问响应
    """
    return error_response(
        message=message,
        code=StatusCode.FORBIDDEN
    )


def internal_error_response(
    message: str = ResponseMessage.INTERNAL_ERROR,
    data: Any = None
) -> ApiResponse[Any]:
    """
    创建服务器内部错误响应
    
    Args:
        message: 错误消息
        data: 错误详情数据
        
    Returns:
        ApiResponse: 服务器内部错误响应
    """
    return error_response(
        message=message,
        code=StatusCode.INTERNAL_SERVER_ERROR,
        data=data
    )


def database_error_response(
    message: str = ResponseMessage.DATABASE_ERROR,
    data: Any = None
) -> ApiResponse[Any]:
    """
    创建数据库错误响应
    
    Args:
        message: 错误消息
        data: 错误详情数据
        
    Returns:
        ApiResponse: 数据库错误响应
    """
    return error_response(
        message=message,
        code=StatusCode.INTERNAL_SERVER_ERROR,
        data=data
    )


def create_json_response(
    api_response: ApiResponse,
    http_status_code: int = 200
) -> JSONResponse:
    """
    将ApiResponse转换为JSONResponse
    
    Args:
        api_response: 统一格式的API响应
        http_status_code: HTTP状态码
        
    Returns:
        JSONResponse: FastAPI的JSON响应对象
    """
    return JSONResponse(
        status_code=http_status_code,
        content=api_response.dict()
    )


def raise_http_exception(
    message: str,
    code: int = StatusCode.INTERNAL_SERVER_ERROR,
    data: Any = None
) -> None:
    """
    抛出HTTP异常，使用统一的响应格式
    
    Args:
        message: 错误消息
        code: 错误状态码
        data: 错误数据
        
    Raises:
        HTTPException: FastAPI的HTTP异常
    """
    error_resp = error_response(message=message, code=code, data=data)
    
    # 将业务状态码映射到HTTP状态码
    http_status_code = _map_business_code_to_http_status(code)
    
    raise HTTPException(
        status_code=http_status_code,
        detail=error_resp.dict()
    )


def _map_business_code_to_http_status(business_code: int) -> int:
    """
    将业务状态码映射到HTTP状态码
    
    Args:
        business_code: 业务状态码
        
    Returns:
        int: 对应的HTTP状态码
    """
    # 200-299 范围映射到 200
    if 200 <= business_code <= 299:
        return 200
    
    # 400-499 范围保持不变
    if 400 <= business_code <= 499:
        return business_code
    
    # 500-599 范围保持不变
    if 500 <= business_code <= 599:
        return business_code
    
    # 默认返回 500
    return 500 