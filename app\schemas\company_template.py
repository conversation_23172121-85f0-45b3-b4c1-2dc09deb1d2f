"""
公司模版相关的数据模型
用于API请求和响应的数据验证
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator


class AnnotationItem(BaseModel):
    """
    标注项目模型
    """
    id: float = Field(..., description="标注项目ID")
    x: int = Field(..., description="X坐标")
    y: int = Field(..., description="Y坐标")
    width: int = Field(..., description="宽度")
    height: int = Field(..., description="高度")
    name: str = Field(..., description="标注名称")
    backgroundColor: str = Field(..., description="背景颜色")
    type: str = Field(..., description="标注类型")
    left_padding: Optional[int] = Field(None, description="左边距（仅header类型）")
    right_padding: Optional[int] = Field(None, description="右边距（仅header类型）")
    bottom_padding: Optional[int] = Field(None, description="下边距（仅header类型）")
    
    class Config:
        json_schema_extra = {
            "example": {
                "id": 1752292735859.8044,
                "x": 481,
                "y": 344,
                "width": 494,
                "height": 86,
                "name": "品名/规格",
                "backgroundColor": "#69d229FF",
                                        "type": "header",
                        "left_padding": 10,
                        "right_padding": 15,
                        "bottom_padding": 5
            }
        }


class CompanyTemplateBase(BaseModel):
    """
    公司模版基础模型
    """
    company_name: str = Field(..., min_length=1, max_length=200, description="公司名称")
    document_image: str = Field(..., description="单据图片路径")
    extract_fields: List[str] = Field(..., description="提取字段数组")
    content_height: int = Field(..., ge=0, description="内容高度")
    annotations: List[AnnotationItem] = Field(..., description="标注信息数组")
    
    @validator('company_name')
    def validate_company_name(cls, v):
        """验证公司名称"""
        if not v or not v.strip():
            raise ValueError('公司名称不能为空')
        return v.strip()
    
    @validator('extract_fields')
    def validate_extract_fields(cls, v):
        """验证提取字段"""
        if not v:
            raise ValueError('提取字段不能为空')
        return v
    
    @validator('annotations')
    def validate_annotations(cls, v):
        """验证标注信息"""
        if not v:
            raise ValueError('标注信息不能为空')
        return v


class CompanyTemplateCreate(CompanyTemplateBase):
    """
    创建公司模版请求模型
    """
    id: Optional[int] = Field(None, description="模版ID，如果提供且存在则更新，否则创建新模版")


class CompanyTemplateCreateWithOCR(BaseModel):
    """
    创建/更新公司模版请求模型（支持OCR，用于表单提交）
    """
    id: Optional[int] = Field(None, description="模版ID，如果提供且存在则更新，否则创建新模版")
    company_name: str = Field(..., min_length=1, max_length=200, description="公司名称")
    annotations: str = Field(..., description="标注信息数组（JSON格式）")
    confidence_threshold: float = Field(0.7, description="OCR置信度阈值")
    
    @validator('company_name')
    def validate_company_name(cls, v):
        """验证公司名称"""
        if not v or not v.strip():
            raise ValueError('公司名称不能为空')
        return v.strip()


class CompanyTemplateUpdate(BaseModel):
    """
    更新公司模版请求模型
    """
    company_name: Optional[str] = Field(None, min_length=1, max_length=200, description="公司名称")
    document_image: Optional[str] = Field(None, description="单据图片路径")
    extract_fields: Optional[List[str]] = Field(None, description="提取字段数组")
    content_height: Optional[int] = Field(None, ge=0, description="内容高度")
    annotations: Optional[List[AnnotationItem]] = Field(None, description="标注信息数组")
    
    @validator('company_name')
    def validate_company_name(cls, v):
        """验证公司名称"""
        if v is not None and (not v or not v.strip()):
            raise ValueError('公司名称不能为空')
        return v.strip() if v else v
    
    @validator('extract_fields')
    def validate_extract_fields(cls, v):
        """验证提取字段"""
        if v is not None and not v:
            raise ValueError('提取字段不能为空')
        return v
    
    @validator('annotations')
    def validate_annotations(cls, v):
        """验证标注信息"""
        if v is not None and not v:
            raise ValueError('标注信息不能为空')
        return v


class CompanyTemplateResponse(CompanyTemplateBase):
    """
    公司模版响应模型
    """
    id: int = Field(..., description="公司模版ID")
    created_at: Optional[str] = Field(None, description="创建时间")
    updated_at: Optional[str] = Field(None, description="更新时间")
    is_deleted: bool = Field(False, description="是否已删除")
    
    # 图片相关字段
    image_uuid: Optional[str] = Field(None, description="图片UUID")
    image_filename: Optional[str] = Field(None, description="原始文件名")
    image_minio_path: Optional[str] = Field(None, description="MinIO存储路径")
    image_upload_time: Optional[str] = Field(None, description="图片上传时间")
    image_access_url: Optional[str] = Field(None, description="图片完整访问链接")
    
    class Config:
        json_schema_extra = {
            "example": {
                "id": 1,
                "company_name": "测试公司",
                "document_image": "/uploads/test.jpg",
                "extract_fields": ["品名/规格", "数量", "单价"],
                "content_height": 100,
                "annotations": [
                    {
                        "id": 1752292735859.8044,
                        "x": 481,
                        "y": 344,
                        "width": 494,
                        "height": 86,
                        "name": "品名/规格",
                        "backgroundColor": "#69d229FF",
                        "type": "header",
                        "left_padding": 10,
                        "right_padding": 15,
                        "bottom_padding": 5
                    }
                ],
                "created_at": "2024-01-01T00:00:00",
                "updated_at": "2024-01-01T00:00:00",
                "is_deleted": False
            }
        }


class CompanyTemplateListItem(BaseModel):
    """
    公司模版列表项模型
    """
    id: int = Field(..., description="公司模版ID")
    company_name: str = Field(..., description="公司名称")
    document_image: str = Field(..., description="单据图片路径")
    extract_fields: List[str] = Field(..., description="提取字段数组")
    created_at: Optional[str] = Field(None, description="创建时间")
    updated_at: Optional[str] = Field(None, description="更新时间")
    
    # 图片相关字段
    image_uuid: Optional[str] = Field(None, description="图片UUID")
    image_filename: Optional[str] = Field(None, description="原始文件名")
    image_minio_path: Optional[str] = Field(None, description="MinIO存储路径")
    image_upload_time: Optional[str] = Field(None, description="图片上传时间")
    image_access_url: Optional[str] = Field(None, description="图片完整访问链接")
    
    class Config:
        json_schema_extra = {
            "example": {
                "id": 1,
                "company_name": "测试公司",
                "document_image": "/uploads/test.jpg",
                "extract_fields": ["品名/规格", "数量", "单价"],
                "created_at": "2024-01-01T00:00:00",
                "updated_at": "2024-01-01T00:00:00"
            }
        } 