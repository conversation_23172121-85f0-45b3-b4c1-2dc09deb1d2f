"""
文本匹配工具模块
提供高效的文本搜索和匹配功能，参考ocr_search_optimized.py的优化方案
"""
from typing import List, Optional, Any, Dict, Tuple
from app.schemas.ocr_scan import FieldMatchResult
from app.core.logging import get_logger

logger = get_logger(__name__)


def match_company_from_ocr(
    image_result: Any, 
    company_names: List[str], 
    confidence_threshold: float = 0.7
) -> Optional[str]:
    """
    函数1：OCR结果与公司名字精确匹配（优化版）
    参考ocr_search_optimized.py的高效算法
    
    Args:
        image_result: OCR识别结果（Surya库返回的image_result对象）
        company_names: 公司名字字符串数组
        confidence_threshold: 置信度阈值
        
    Returns:
        Optional[str]: 第一个匹配的公司名字，如果没有匹配则返回None
    """
    if not company_names:
        return None
    
    # 将公司名称转换为集合以提高查找效率 O(1)
    company_set = set(company_names)
    
    # 遍历OCR结果中的文本行
    for text_line in image_result.text_lines:
        # 检查置信度阈值
        if text_line.confidence < confidence_threshold:
            continue
        
        # 使用集合查找进行精确匹配 O(1)
        if text_line.text in company_set:
            logger.info(f"找到匹配的公司: {text_line.text}, 置信度: {text_line.confidence}")
            return text_line.text
    
    logger.info("未找到匹配的公司")
    return None


def match_fields_from_ocr(
    image_result: Any,
    field_names: List[str],
    confidence_threshold: float = 0.7
) -> List[FieldMatchResult]:
    """
    函数2：OCR结果与字段精确匹配（优化版）
    参考ocr_search_optimized.py的高效算法
    
    Args:
        image_result: OCR识别结果（Surya库返回的image_result对象）
        field_names: 字段字符串数组
        confidence_threshold: 置信度阈值
        
    Returns:
        List[FieldMatchResult]: 详细匹配信息列表（包含位置、置信度等）
    """
    if not field_names:
        return []
    
    # 将字段名称转换为集合以提高查找效率 O(1)
    field_set = set(field_names)
    field_matches = []
    
    # 遍历OCR结果中的文本行
    for text_line in image_result.text_lines:
        # 检查置信度阈值
        if text_line.confidence < confidence_threshold:
            continue
        
        # 使用集合查找进行精确匹配 O(1)
        if text_line.text in field_set:
            field_match = FieldMatchResult(
                field_name=text_line.text,
                matched_text=text_line.text,
                confidence=text_line.confidence,
                text_position=text_line.bbox
            )
            field_matches.append(field_match)
            logger.info(f"找到匹配字段: {text_line.text}, 置信度: {text_line.confidence}")
            # logger.info(f"字段位置: {text_line.polygon}")
            logger.info(f"字段位置: {text_line.bbox}")
    
    logger.info(f"字段匹配完成，找到 {len(field_matches)} 个匹配字段")
    return field_matches


def calculate_padding_from_ocr_and_annotations(
    image_result: Any,
    annotations: List[Dict[str, Any]],
    confidence_threshold: float = 0.7
) -> List[Dict[str, Any]]:
    """
    根据OCR识别结果和annotations配置计算padding值，并直接添加到annotations中
    
    Args:
        image_result: OCR识别结果（Surya库返回的image_result对象）
        annotations: 标注信息列表，每个元素包含 {name, x, y, width, height, type, ...}
        confidence_threshold: 置信度阈值
        
    Returns:
        List[Dict[str, Any]]: 修改后的annotations数组，header类型的项目会包含left_padding、right_padding和bottom_padding
    """
    if not annotations:
        logger.warning("annotations为空，返回原始annotations")
        return annotations
    
    # 创建annotations的深拷贝，避免修改原始数据
    import copy
    modified_annotations = copy.deepcopy(annotations)
    
    # 只提取header类型的annotation的name字段 (支持'header'和'headr'两种拼写)
    header_annotations = []
    annotation_names = []
    
    for i, ann in enumerate(modified_annotations):
        if ann.get('type', '').lower() in ['header', 'headr']:
            name = ann.get('name')
            if name:
                header_annotations.append((i, ann))  # 保存索引和annotation
                annotation_names.append(name)
    
    if not annotation_names:
        logger.warning("annotations中没有header类型的name字段，返回原始annotations")
        return modified_annotations
    
    # 使用现有的字段匹配函数
    field_matches = match_fields_from_ocr(
        image_result=image_result,
        field_names=annotation_names,
        confidence_threshold=confidence_threshold
    )
    
    if not field_matches:
        logger.warning("未找到匹配的字段，返回原始annotations")
        return modified_annotations
    
    # 创建name到annotation索引的映射
    name_to_index = {}
    for index, ann in header_annotations:
        name = ann.get('name')
        if name:
            name_to_index[name] = index
    
    # 计算每个匹配字段的padding值并添加到对应的annotation中
    for field_match in field_matches:
        field_name = field_match.field_name
        annotation_index = name_to_index.get(field_name)
        
        if annotation_index is None:
            logger.warning(f"未找到字段 {field_name} 对应的annotation索引")
            continue
        
        annotation = modified_annotations[annotation_index]
        
        # OCR识别的位置 [x1, y1, x2, y2]
        ocr_x1, ocr_y1, ocr_x2, ocr_y2 = field_match.text_position
        
        # annotation的位置信息
        ann_x = annotation.get('x', 0)
        ann_y = annotation.get('y', 0)
        ann_width = annotation.get('width', 0)
        ann_height = annotation.get('height', 0)
        
        # 计算padding值
        # left_padding = annotation_x - ocr_x1
        # right_padding = (annotation_x + annotation_width) - ocr_x2
        # bottom_padding = (annotation_y + annotation_height) - ocr_y2
        left_padding = ann_x - ocr_x1
        right_padding = (ann_x + ann_width) - ocr_x2
        bottom_padding = (ann_y + ann_height) - ocr_y2
        
        # 将padding值添加到annotation对象中
        modified_annotations[annotation_index]['left_padding'] = left_padding
        modified_annotations[annotation_index]['right_padding'] = right_padding
        modified_annotations[annotation_index]['bottom_padding'] = bottom_padding
        
        # 计算用户标注的右下角坐标
        ann_x2 = ann_x + ann_width
        ann_y2 = ann_y + ann_height
        
        logger.info(f"表头字段: {field_name}")
        logger.info(f"  用户标注 - 左上角: ({ann_x}, {ann_y}), 右下角: ({ann_x2}, {ann_y2})")
        logger.info(f"  OCR识别 - 左上角: ({ocr_x1}, {ocr_y1}), 右下角: ({ocr_x2}, {ocr_y2})")
    
    logger.info(f"Padding计算完成，共处理了 {len(field_matches)} 个字段")
    
    return modified_annotations


def analyze_field_boundaries(ocr_result, annotations):
    """
    分析每个字段的边界信息
    
    Args:
        ocr_result: OCR识别结果对象
        annotations: 标注信息数组
        
    Returns:
        List[Dict]: 字段边界信息数组
        [
            {
                "field_name": "订单号码",
                "left_boundary": 250,
                "right_boundary": 370,
                "start_height": 465
            }
        ]
    """
    # 只处理header类型的标注
    header_annotations = [ann for ann in annotations if ann.get('type') == 'header']
    
    field_boundaries = []
    
    for header in header_annotations:
        field_name = header['name']
        
        # 在OCR结果中找到对应的字段文本
        ocr_field = None
        for ocr_line in ocr_result.text_lines:
            if ocr_line.text == field_name:
                ocr_field = ocr_line
                break
        
        if ocr_field:
            # OCR字段的边界框 [x1, y1, x2, y2]
            ocr_x1, ocr_y1, ocr_x2, ocr_y2 = ocr_field.bbox
            
            # 计算边界
            left_boundary = ocr_x1 + header.get('left_padding', 0)
            right_boundary = ocr_x2 + header.get('right_padding', 0)
            start_height = ocr_y2 + header.get('bottom_padding', 0)
            
            field_boundary = {
                "field_name": field_name,
                "left_boundary": left_boundary,
                "right_boundary": right_boundary,
                "start_height": start_height
            }
            
            field_boundaries.append(field_boundary)
    
    return field_boundaries


def extract_table_data(ocr_result, field_boundaries, content_height):
    """
    提取表格数据
    
    Args:
        ocr_result: OCR识别结果对象
        field_boundaries: analyze_field_boundaries返回的对象数组
        content_height: 内容高度
        
    Returns:
        List[Dict]: 表格数据
    """
    table_data = []
    
    for row_index in range(5):  # 最大6行
        row_data = {"row_index": row_index}
        found_any_data = False
        
        for field_boundary in field_boundaries:  # 遍历每个字段
            # 计算当前行当前字段的坐标范围
            current_region = {
                'left': field_boundary['left_boundary'],
                'right': field_boundary['right_boundary'],
                'top': field_boundary['start_height'],
                'bottom': field_boundary['start_height'] + content_height
            }
            
            # 在OCR结果中查找符合坐标范围的文本
            for ocr_line in ocr_result.text_lines:
                if is_ocr_text_in_region(ocr_line.bbox, current_region):
                    row_data[field_boundary['field_name']] = ocr_line.text
                    found_any_data = True
                    break  # 找到第一个匹配的就停止
        
        # 如果这一行找到了数据，就添加到结果中
        if found_any_data:
            table_data.append(row_data)
        
        # 更新所有字段的起始高度（修改原始数组）
        for field_boundary in field_boundaries:
            field_boundary['start_height'] += content_height
    
    return table_data


def is_ocr_text_in_region(ocr_bbox, region):
    """
    判断OCR文本是否完全在区域内
    """
    ocr_x1, ocr_y1, ocr_x2, ocr_y2 = ocr_bbox
    
    # OCR文本完全在区域内
    return (ocr_x1 >= region['left'] and 
            ocr_x2 <= region['right'] and
            ocr_y1 >= region['top'] and 
            ocr_y2 <= region['bottom'])


 