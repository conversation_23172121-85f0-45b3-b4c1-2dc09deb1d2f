# FastAPI OCR 项目配置文件模板
# 复制此文件为 .env 并根据需要修改配置

# ===== 应用基础配置 =====
APP_NAME=FastAPI OCR Project
APP_VERSION=1.0.0
APP_ENV=dev
DEBUG=true

# ===== 服务器配置 =====
HOST=0.0.0.0
PORT=8000

# ===== 数据库配置 =====
# 开发环境使用 SQLite
DATABASE_URL=sqlite+aiosqlite:///./app.db
# 生产环境使用 PostgreSQL
# DATABASE_URL=postgresql+asyncpg://username:password@localhost:5432/fastapi_ocr
DATABASE_ECHO=false

# ===== Redis 配置 (可选) =====
# REDIS_URL=redis://localhost:6379/0

# ===== 日志配置 =====
LOG_LEVEL=INFO
# LOG_FILE=logs/app.log
LOG_ROTATION=1 day
LOG_RETENTION=30 days

# ===== CORS 配置 =====
CORS_ORIGINS=["http://localhost:3000","http://localhost:8080","http://127.0.0.1:3000"]
CORS_CREDENTIALS=true
CORS_METHODS=["GET","POST","PUT","DELETE","OPTIONS"]
CORS_HEADERS=["*"]

# ===== JWT 配置 (预留) =====
SECRET_KEY=your-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# ===== 文件上传配置 =====
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760
ALLOWED_EXTENSIONS=["jpg","jpeg","png","gif","pdf"]

# ===== MinIO 对象存储配置 =====
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_SECURE=false
MINIO_BUCKET_NAME=ocr-images
ENABLE_MINIO_STORAGE=true

# ===== 生产环境专用配置 =====
# 生产环境请务必修改以下配置
# SECRET_KEY=your-very-secure-secret-key-here
# DATABASE_URL=postgresql+asyncpg://username:password@localhost:5432/fastapi_ocr_prod
# DEBUG=false
# LOG_LEVEL=WARNING 