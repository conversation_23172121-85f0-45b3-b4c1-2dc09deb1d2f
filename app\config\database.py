"""
数据库配置
异步数据库引擎配置和会话管理
"""
from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.orm import DeclarativeBase
from sqlalchemy.pool import StaticPool
from sqlalchemy import text

from .settings import settings


class Base(DeclarativeBase):
    """数据库模型基类"""
    pass


# 导入所有模型以确保它们被注册到Base.metadata中
def import_models():
    """导入所有数据库模型"""
    from ..models import CompanyTemplate, OCRTask
    return CompanyTemplate, OCRTask


def create_engine():
    """创建数据库引擎"""
    database_url = settings.database_url
    
    # 根据数据库类型配置引擎参数
    if "sqlite" in database_url:
        # SQLite 配置
        engine = create_async_engine(
            database_url,
            echo=settings.DATABASE_ECHO,
            poolclass=StaticPool,
            connect_args={
                "check_same_thread": False,
                "timeout": 30,
            }
        )
    elif "mysql" in database_url:
        # MySQL 配置
        engine = create_async_engine(
            database_url,
            echo=settings.DATABASE_ECHO,
            pool_size=settings.DB_POOL_SIZE,
            max_overflow=settings.DB_MAX_OVERFLOW,
            pool_pre_ping=True,
            pool_recycle=3600,
            connect_args={
                "charset": "utf8mb4",
                "autocommit": False,
            }
        )
    else:
        # 不支持的数据库类型
        raise ValueError("Unsupported database type in DATABASE_URL. Only MySQL and SQLite are supported.")
    
    return engine


# 全局数据库引擎实例
engine = create_engine()

# 创建异步会话工厂
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autocommit=False,
    autoflush=False,
)


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    获取数据库会话
    用于 FastAPI 依赖注入
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def init_db():
    """初始化数据库"""
    async with engine.begin() as conn:
        # 创建所有表
        await conn.run_sync(Base.metadata.create_all)




async def close_db():
    """关闭数据库连接"""
    await engine.dispose()


# 数据库健康检查
async def check_db_health() -> bool:
    """检查数据库连接健康状态"""
    try:
        async with AsyncSessionLocal() as session:
            # 执行简单查询检查连接
            result = await session.execute("SELECT 1")
            return result.scalar() == 1
    except Exception:
        return False 