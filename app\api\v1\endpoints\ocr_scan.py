"""
OCR扫描API端点
提供文档OCR分析功能
"""
import time
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_

from app.config.database import get_db
from app.models.ocr_task import OCRTask
from app.schemas.ocr_scan import OCRMatchResponse, OCRTableDataUpdateRequest
from app.schemas.response import ApiResponse, StatusCode, ResponseMessage
from app.utils.file_handler import validate_and_load_image, FileHandler
from app.utils.ocr_processor import (
    perform_ocr_recognition,
    validate_ocr_result
)
from app.utils.company_service import (
    get_all_company_names,
    get_company_by_name
)
from app.utils.text_matcher import (
    match_company_from_ocr, 
    match_fields_from_ocr,
    analyze_field_boundaries,
    extract_table_data
)
from app.utils.minio_client import get_image_url
from app.schemas.ocr_task import OCRTaskResponse, OCRTaskListItem
from app.schemas.pagination import PaginationParams, create_paginated_data
from app.core.logging import get_logger

logger = get_logger(__name__)

router = APIRouter()

# 文件处理器实例
file_handler = FileHandler()


@router.post(
    "/analyze-document",
    response_model=ApiResponse[OCRMatchResponse],
    summary="OCR文档分析",
    description="上传图片进行OCR识别，匹配公司信息，并进行字段搜索"
)
async def analyze_document(
    file: UploadFile = File(..., description="要分析的图片文件"),
    confidence_threshold: float = Form(0.7, description="置信度阈值 (0.0-1.0)"),
    db: AsyncSession = Depends(get_db)
):
    """
    OCR文档分析API
    
    功能流程：
    1. 用户上传图片 → 进行OCR识别
    2. 读取数据库内所有公司名字
    3. 使用函数1匹配公司
    4. 如果没匹配到公司 → 返回错误
    5. 根据公司名查询数据库获取字段信息
    6. 使用函数2匹配字段
    7. 如果没匹配到字段 → 返回错误
    8. 返回匹配结果
    
    Args:
        file: 上传的图片文件
        confidence_threshold: 置信度阈值
        db: 数据库会话
        
    Returns:
        ApiResponse[OCRAnalysisResponse]: 分析结果响应
    """
    analysis_start_time = time.time()
    
    try:
        logger.info(f"开始OCR文档分析: {file.filename}")
        
        # 1. 验证并加载图片
        try:
            image = await validate_and_load_image(file)
        except HTTPException as e:
            logger.error(f"图片验证失败: {e.detail}")
            raise HTTPException(
                status_code=StatusCode.BAD_REQUEST,
                detail=f"图片验证失败: {e.detail}"
            )
        
        # 2. 进行OCR识别
        try:
            image_result = await perform_ocr_recognition(image)
        except Exception as e:
            logger.error(f"OCR识别失败: {e}")
            raise HTTPException(
                status_code=StatusCode.INTERNAL_SERVER_ERROR,
                detail=f"OCR识别失败: {str(e)}"
            )
        
        # 3. 验证OCR结果
        if not validate_ocr_result(image_result):
            logger.warning("OCR识别结果无效或为空")
            raise HTTPException(
                status_code=StatusCode.BAD_REQUEST,
                detail="图片中未识别到有效文本，请确保图片清晰且包含文字内容"
            )
        
        # 4. 读取数据库内所有公司名字
        try:
            company_names = await get_all_company_names(db)
        except Exception as e:
            logger.error(f"获取公司名称失败: {e}")
            raise HTTPException(
                status_code=StatusCode.INTERNAL_SERVER_ERROR,
                detail=f"获取公司信息失败: {str(e)}"
            )
        
        if not company_names:
            logger.warning("数据库中没有公司信息")
            raise HTTPException(
                status_code=StatusCode.NOT_FOUND,
                detail="系统中暂无公司信息，请先添加公司模板"
            )
        
        # 5. 使用函数1匹配公司
        matched_company_name = match_company_from_ocr(
            image_result=image_result,
            company_names=company_names,
            confidence_threshold=confidence_threshold
        )
        
        # 6. 如果没匹配到公司 → 返回错误
        if not matched_company_name:
            logger.warning("未找到匹配的公司")
            raise HTTPException(
                status_code=StatusCode.NOT_FOUND,
                detail="未找到匹配的公司，请确保图片中包含系统中已注册的公司信息"
            )
        
        # 7. 根据公司名查询数据库获取完整信息
        try:
            company_info = await get_company_by_name(db, matched_company_name)
        except Exception as e:
            logger.error(f"查询公司信息失败: {e}")
            raise HTTPException(
                status_code=StatusCode.INTERNAL_SERVER_ERROR,
                detail=f"查询公司信息失败: {str(e)}"
            )
        
        if not company_info:
            logger.error(f"公司信息不存在: {matched_company_name}")
            raise HTTPException(
                status_code=StatusCode.NOT_FOUND,
                detail=f"公司信息不存在: {matched_company_name}"
            )
        
        # 8. 使用新的表格数据提取功能
        # 8.1 分析字段边界
        field_boundaries = analyze_field_boundaries(
            ocr_result=image_result,
            annotations=company_info.annotations
        )
        
        # 8.2 检查是否找到字段边界
        if not field_boundaries:
            logger.warning(f"公司 {matched_company_name} 未找到字段边界信息")
            raise HTTPException(
                status_code=StatusCode.NOT_FOUND,
                detail=f"未找到字段边界信息，请确保图片中包含公司模板定义的字段"
            )
        
        # 8.3 提取表格数据
        table_data = extract_table_data(
            ocr_result=image_result,
            field_boundaries=field_boundaries,
            content_height=company_info.content_height
        )
        
        # 8.4 检查是否提取到数据
        if not table_data:
            logger.warning("未提取到表格数据")
            raise HTTPException(
                status_code=StatusCode.NOT_FOUND,
                detail="未提取到表格数据，请确保图片中包含有效的表格内容"
            )
        
        # 9. 保存图片到 MinIO（OCR 识别成功后）
        image_info = None
        try:
            image_info = await file_handler.save_image_to_minio(
                file, 
                folder="ocr-tasks"
            )
            if image_info:
                logger.info(f"OCR 任务图片保存到 MinIO 成功: {image_info[1]}")
            else:
                logger.warning("OCR 任务图片保存到 MinIO 失败")
        except Exception as e:
            logger.error(f"保存 OCR 任务图片到 MinIO 异常: {e}")
        
        # 10. 计算处理时间和基础信息
        analysis_time = time.time() - analysis_start_time
        field_names = [boundary['field_name'] for boundary in field_boundaries]
        
        # 11. 准备响应数据的基础字段
        response_data_dict = {
            "company_name": matched_company_name,
            "table_data": table_data,
            "table_rows": len(table_data),
            "field_names": field_names,
            "processing_time": round(analysis_time, 2),
            "confidence_threshold": confidence_threshold,
            "content_height": company_info.content_height
        }
        
        # 如果图片保存成功，添加图片信息
        if image_info:
            image_uuid, minio_path, original_filename, upload_time = image_info
            response_data_dict.update({
                "image_uuid": image_uuid,
                "image_filename": original_filename,
                "image_minio_path": minio_path,
                "image_upload_time": upload_time.isoformat()
            })
        
        # 12. 保存OCR任务到数据库（如果图片保存成功）
        if image_info:
            try:
                image_uuid, minio_path, original_filename, upload_time = image_info
                
                # 创建OCR任务数据
                ocr_task_data = {
                    'company_name': matched_company_name,
                    'table_data': table_data,
                    'table_rows': len(table_data),
                    'field_names': field_names,
                    'processing_time': round(analysis_time, 2),
                    'confidence_threshold': confidence_threshold,
                    'content_height': company_info.content_height,
                    'image_uuid': image_uuid,
                    'image_filename': original_filename,
                    'image_minio_path': minio_path,
                    'image_upload_time': upload_time
                }
                
                # 创建并保存OCR任务
                new_ocr_task = OCRTask.create_from_dict(ocr_task_data)
                db.add(new_ocr_task)
                await db.commit()
                await db.refresh(new_ocr_task)
                
                logger.info(f"OCR任务保存成功: {new_ocr_task.id}")
                
                # 将任务ID添加到响应数据中
                response_data_dict['id'] = new_ocr_task.id
                
            except Exception as e:
                logger.error(f"保存OCR任务失败: {e}")
                # 不影响主流程，继续返回结果
        
        # 13. 构建响应数据（包含表格数据）
        response_data = OCRMatchResponse(**response_data_dict)
        
        logger.info(f"OCR文档分析完成: {file.filename}, 耗时: {analysis_time:.2f}秒")
        logger.info(f"匹配结果: 公司={matched_company_name}, 表格行数={len(table_data)}")
        
        return ApiResponse(
            code=StatusCode.SUCCESS,
            message=ResponseMessage.SUCCESS,
            data=response_data
        )
        
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"OCR文档分析过程中发生未知错误: {e}")
        raise HTTPException(
            status_code=StatusCode.INTERNAL_SERVER_ERROR,
            detail=f"文档分析失败: {str(e)}"
        )


@router.get(
    "/history",
    response_model=ApiResponse,
    summary="获取OCR扫描历史记录列表",
    description="获取OCR扫描历史记录信息列表，支持分页和搜索"
)
async def get_ocr_scan_history(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页大小"),
    keyword: Optional[str] = Query(None, description="搜索关键词（公司名称）"),
    db: AsyncSession = Depends(get_db)
):
    """
    获取OCR扫描历史记录列表
    
    Args:
        page: 页码，从1开始
        size: 每页大小，最大100
        keyword: 搜索关键词，用于搜索公司名称
        db: 数据库会话
        
    Returns:
        ApiResponse: 包含OCR扫描历史记录列表的响应
    """
    try:
        # 构建基础查询
        query = select(OCRTask).where(OCRTask.is_deleted == False)
        
        # 添加搜索条件
        if keyword:
            query = query.where(OCRTask.company_name.contains(keyword))
        
        # 获取总数
        count_query = select(func.count(OCRTask.id)).where(
            and_(
                OCRTask.is_deleted == False,
                OCRTask.company_name.contains(keyword) if keyword else True
            )
        )
        total_result = await db.execute(count_query)
        total = total_result.scalar() or 0
        
        # 分页查询
        offset = (page - 1) * size
        query = query.order_by(OCRTask.created_at.desc()).offset(offset).limit(size)
        
        result = await db.execute(query)
        tasks = result.scalars().all()
        
        # 转换为响应模型
        task_list = []
        for task in tasks:
            task_data = task.to_dict_summary()
            
            # 如果存在MinIO路径，生成完整的访问链接
            if task.image_minio_path:
                try:
                    image_url = await get_image_url(task.image_minio_path)
                    if image_url:
                        task_data['image_access_url'] = image_url
                except Exception as e:
                    logger.error(f"生成图片访问链接异常 (任务ID: {task.id}): {e}")
            
            task_list.append(OCRTaskListItem(**task_data))
        
        # 创建分页数据
        paginated_data = create_paginated_data(
            items=task_list,
            page=page,
            size=size,
            total=total
        )
        
        logger.info(f"获取OCR扫描历史记录列表成功，返回{len(task_list)}条记录")
        
        return ApiResponse(
            code=StatusCode.SUCCESS,
            message=ResponseMessage.SUCCESS,
            data=paginated_data.dict()
        )
        
    except Exception as e:
        logger.error(f"获取OCR扫描历史记录列表失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取OCR扫描历史记录列表失败: {str(e)}"
        )


@router.get(
    "/history/{task_id}",
    response_model=ApiResponse[OCRTaskResponse],
    summary="获取OCR扫描记录详情",
    description="根据ID获取单个OCR扫描记录的详细信息"
)
async def get_ocr_scan_detail(
    task_id: int = Path(..., description="OCR扫描记录ID"),
    db: AsyncSession = Depends(get_db)
):
    """
    获取OCR扫描记录详情
    
    Args:
        task_id: OCR扫描记录ID
        db: 数据库会话
        
    Returns:
        ApiResponse: 包含OCR扫描记录详细信息的响应
    """
    try:
        # 查询任务
        query = select(OCRTask).where(
            and_(
                OCRTask.id == task_id,
                OCRTask.is_deleted == False
            )
        )
        
        result = await db.execute(query)
        task = result.scalar_one_or_none()
        
        if not task:
            logger.warning(f"OCR扫描记录不存在: {task_id}")
            raise HTTPException(
                status_code=404,
                detail="OCR扫描记录不存在"
            )
        
        # 转换为响应模型
        task_data = task.to_dict_detail()
        
        # 如果存在MinIO路径，生成完整的访问链接
        if task_data.get('image_minio_path'):
            try:
                image_url = await get_image_url(task_data['image_minio_path'])
                if image_url:
                    task_data['image_access_url'] = image_url
                    logger.info(f"生成图片访问链接成功: {task_id}")
                else:
                    logger.warning(f"生成图片访问链接失败: {task_id}")
            except Exception as e:
                logger.error(f"生成图片访问链接异常: {e}")
        
        task_response = OCRTaskResponse(**task_data)
        
        logger.info(f"获取OCR扫描记录详情成功: {task_id}")
        
        return ApiResponse(
            code=StatusCode.SUCCESS,
            message=ResponseMessage.SUCCESS,
            data=task_response
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取OCR扫描记录详情失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取OCR扫描记录详情失败: {str(e)}"
        )


@router.put(
    "/history/table-data",
    response_model=ApiResponse[OCRTaskResponse],
    summary="更新OCR扫描记录的表格数据",
    description="根据请求体中的ID更新指定OCR扫描记录的table_data字段"
)
async def update_ocr_table_data(
    update_data: OCRTableDataUpdateRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    更新OCR扫描记录的表格数据
    
    Args:
        update_data: 包含id和新table_data的更新请求
        db: 数据库会话
        
    Returns:
        ApiResponse: 包含更新后的OCR扫描记录详细信息的响应
    """
    try:
        task_id = update_data.id
        logger.info(f"开始更新OCR扫描记录表格数据: {task_id}")
        
        # 查询任务
        query = select(OCRTask).where(
            and_(
                OCRTask.id == task_id,
                OCRTask.is_deleted == False
            )
        )
        
        result = await db.execute(query)
        task = result.scalar_one_or_none()
        
        if not task:
            logger.warning(f"OCR扫描记录不存在: {task_id}")
            raise HTTPException(
                status_code=404,
                detail="OCR扫描记录不存在"
            )
        
        # 更新表格数据
        task.table_data = update_data.table_data
        task.table_rows = len(update_data.table_data)
        task.is_table_data_modified = True  # 标记表格数据已被修改
        task.updated_at = func.now()
        
        # 提交更新
        await db.commit()
        await db.refresh(task)
        
        logger.info(f"OCR扫描记录表格数据更新成功: {task_id}, 新行数: {len(update_data.table_data)}")
        
        # 转换为响应模型
        task_data = task.to_dict_detail()
        
        # 如果存在MinIO路径，生成完整的访问链接
        if task_data.get('image_minio_path'):
            try:
                image_url = await get_image_url(task_data['image_minio_path'])
                if image_url:
                    task_data['image_access_url'] = image_url
                    logger.info(f"生成图片访问链接成功: {task_id}")
                else:
                    logger.warning(f"生成图片访问链接失败: {task_id}")
            except Exception as e:
                logger.error(f"生成图片访问链接异常: {e}")
        
        task_response = OCRTaskResponse(**task_data)
        
        return ApiResponse(
            code=StatusCode.SUCCESS,
            message=ResponseMessage.SUCCESS,
            data=task_response
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新OCR扫描记录表格数据失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"更新OCR扫描记录表格数据失败: {str(e)}"
        ) 