#!/usr/bin/env python3
"""
配置系统测试脚本
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from app.config import init_settings, load_config_file


def test_json_config():
    """测试 JSON 配置加载"""
    print("🧪 测试 JSON 配置加载...")
    
    try:
        # 测试开发环境
        settings = init_settings("dev", "json")
        print(f"✅ DEV JSON 配置加载成功:")
        print(f"   应用名称: {settings.app.name}")
        print(f"   数据库: {settings.database.url}")
        print(f"   调试模式: {settings.app.debug}")
        
        # 测试生产环境
        settings = init_settings("prod", "json")
        print(f"✅ PROD JSON 配置加载成功:")
        print(f"   应用名称: {settings.app.name}")
        print(f"   数据库: {settings.database.url}")
        print(f"   调试模式: {settings.app.debug}")
        
    except Exception as e:
        print(f"❌ JSON 配置测试失败: {e}")


def test_ini_config():
    """测试 INI 配置加载"""
    print("\n🧪 测试 INI 配置加载...")
    
    try:
        # 测试开发环境 INI
        settings = init_settings("dev", "ini")
        print(f"✅ DEV INI 配置加载成功:")
        print(f"   应用名称: {settings.app.name}")
        print(f"   数据库: {settings.database.url}")
        print(f"   调试模式: {settings.app.debug}")
        print(f"   CORS 来源: {settings.cors.origins}")
        
    except Exception as e:
        print(f"❌ INI 配置测试失败: {e}")


def test_auto_config():
    """测试自动格式检测"""
    print("\n🧪 测试自动格式检测...")
    
    try:
        # 自动检测格式
        settings = init_settings("dev", "auto")
        print(f"✅ DEV 自动格式检测成功:")
        print(f"   应用名称: {settings.app.name}")
        print(f"   环境: {settings.env}")
        
    except Exception as e:
        print(f"❌ 自动格式检测测试失败: {e}")


def test_config_validation():
    """测试配置验证"""
    print("\n🧪 测试配置验证...")
    
    try:
        # 测试无效环境
        try:
            init_settings("invalid_env")
            print("❌ 应该抛出无效环境错误")
        except ValueError as e:
            print(f"✅ 无效环境验证成功: {e}")
        
        # 测试无效格式
        try:
            init_settings("dev", "invalid_format")
            print("❌ 应该抛出无效格式错误")
        except ValueError as e:
            print(f"✅ 无效格式验证成功: {e}")
            
    except Exception as e:
        print(f"❌ 配置验证测试失败: {e}")


if __name__ == "__main__":
    print("🚀 开始配置系统测试...\n")
    
    test_json_config()
    test_ini_config()
    test_auto_config()
    test_config_validation()
    
    print("\n🎉 配置系统测试完成!")
