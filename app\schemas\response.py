"""
统一API响应模型
定义标准的响应格式：code、message、data
"""
from typing import Generic, TypeVar, Optional, Any
from pydantic import BaseModel, Field


# 定义泛型类型变量，用于data字段的类型
T = TypeVar('T')


class ApiResponse(BaseModel, Generic[T]):
    """
    统一API响应模型
    
    Args:
        code: 状态码，200-299表示成功，400-599表示各种错误
        message: 响应消息，描述请求执行结果
        data: 响应数据，可以是任意类型
    """
    code: int = Field(..., description="状态码", example=200)
    message: str = Field(..., description="响应消息", example="请求成功")
    data: Optional[T] = Field(None, description="响应数据")
    
    class Config:
        """Pydantic配置"""
        # 允许任意类型的data字段
        arbitrary_types_allowed = True
        # 生成示例
        json_schema_extra = {
            "example": {
                "code": 200,
                "message": "请求成功",
                "data": {}
            }
        }


class PaginatedResponse(BaseModel, Generic[T]):
    """
    分页响应数据模型
    用于包装分页数据，放在ApiResponse的data字段中
    """
    items: list[T] = Field(..., description="数据列表")
    total: int = Field(..., description="总记录数")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
    pages: int = Field(..., description="总页数")
    
    class Config:
        """Pydantic配置"""
        json_schema_extra = {
            "example": {
                "items": [],
                "total": 100,
                "page": 1,
                "size": 20,
                "pages": 5
            }
        }


# 常用的响应类型别名
class SuccessResponse(ApiResponse[T], Generic[T]):
    """成功响应"""
    code: int = Field(200, description="成功状态码")
    message: str = Field("请求成功", description="成功消息")


class ErrorResponse(ApiResponse[None]):
    """错误响应"""
    code: int = Field(400, description="错误状态码")
    message: str = Field("请求失败", description="错误消息")
    data: None = Field(None, description="错误响应无数据")


# 预定义的状态码常量
class StatusCode:
    """状态码常量"""
    # 成功状态码 (200-299)
    SUCCESS = 200
    CREATED = 201
    ACCEPTED = 202
    NO_CONTENT = 204
    
    # 客户端错误状态码 (400-499)
    BAD_REQUEST = 400
    UNAUTHORIZED = 401
    FORBIDDEN = 403
    NOT_FOUND = 404
    METHOD_NOT_ALLOWED = 405
    CONFLICT = 409
    VALIDATION_ERROR = 422
    TOO_MANY_REQUESTS = 429
    
    # 服务器错误状态码 (500-599)
    INTERNAL_SERVER_ERROR = 500
    NOT_IMPLEMENTED = 501
    BAD_GATEWAY = 502
    SERVICE_UNAVAILABLE = 503
    GATEWAY_TIMEOUT = 504


# 预定义的响应消息常量
class ResponseMessage:
    """响应消息常量"""
    # 成功消息
    SUCCESS = "请求成功"
    CREATED = "创建成功"
    UPDATED = "更新成功"
    DELETED = "删除成功"
    
    # 错误消息
    BAD_REQUEST = "请求参数错误"
    UNAUTHORIZED = "未授权访问"
    FORBIDDEN = "禁止访问"
    NOT_FOUND = "资源不存在"
    VALIDATION_ERROR = "数据验证失败"
    INTERNAL_ERROR = "服务器内部错误"
    DATABASE_ERROR = "数据库操作失败"
    NETWORK_ERROR = "网络请求失败"