"""
配置管理系统
基于 Pydantic 实现多环境配置管理，支持从 JSON 和 INI 配置文件加载
"""
import json
import configparser
import os
from pathlib import Path
from typing import Optional, List, Dict, Any, Union
from pydantic import BaseModel, Field, validator


class AppConfig(BaseModel):
    """应用配置"""
    name: str = "FastAPI OCR Project"
    version: str = "1.0.0"
    debug: bool = True
    host: str = "0.0.0.0"
    port: int = 8000


class DatabaseConfig(BaseModel):
    """数据库配置"""
    url: str
    echo: bool = False
    pool_size: int = 10
    max_overflow: int = 20


class RedisConfig(BaseModel):
    """Redis配置"""
    url: str = "redis://localhost:6379/0"


class LoggingConfig(BaseModel):
    """日志配置"""
    level: str = "INFO"
    rotation: str = "1 day"
    retention: str = "30 days"

    @validator('level')
    def validate_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'日志级别必须是 {valid_levels} 之一')
        return v.upper()


class CorsConfig(BaseModel):
    """CORS配置"""
    origins: List[str] = ["*"]
    credentials: bool = True
    methods: List[str] = ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    headers: List[str] = ["*"]


class UploadConfig(BaseModel):
    """文件上传配置"""
    dir: str = "uploads"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    allowed_extensions: List[str] = ["jpg", "jpeg", "png", "gif", "pdf"]


class MinioConfig(BaseModel):
    """MinIO对象存储配置"""
    endpoint: str = "localhost:9000"
    access_key: str = "minioadmin"
    secret_key: str = "minioadmin"
    secure: bool = False
    bucket_name: str = "ocr-images"
    enabled: bool = True


class Settings(BaseModel):
    """应用配置类"""

    # 环境标识
    env: str = "dev"

    # 各模块配置
    app: AppConfig
    database: DatabaseConfig
    redis: RedisConfig
    logging: LoggingConfig
    cors: CorsConfig
    upload: UploadConfig
    minio: MinioConfig

    @property
    def is_development(self) -> bool:
        """判断是否为开发环境"""
        return self.env == "dev"

    @property
    def is_production(self) -> bool:
        """判断是否为生产环境"""
        return self.env == "prod"

    @property
    def is_testing(self) -> bool:
        """判断是否为测试环境"""
        return self.env == "test"


def load_config_from_json(env: str = "dev") -> Dict[str, Any]:
    """
    从 JSON 配置文件加载配置

    Args:
        env: 环境名称 (dev, prod, test)

    Returns:
        Dict[str, Any]: 配置字典

    Raises:
        FileNotFoundError: 配置文件不存在
        json.JSONDecodeError: JSON 格式错误
    """
    # 获取项目根目录
    project_root = Path(__file__).parent.parent.parent
    config_file = project_root / "config" / f"{env}.json"

    if not config_file.exists():
        raise FileNotFoundError(f"配置文件不存在: {config_file}")

    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)

        # 添加环境标识
        config_data['env'] = env
        return config_data

    except json.JSONDecodeError as e:
        raise json.JSONDecodeError(f"配置文件 {config_file} JSON 格式错误: {e}", e.doc, e.pos)


def load_config_from_ini(env: str = "dev") -> Dict[str, Any]:
    """
    从 INI 配置文件加载配置

    Args:
        env: 环境名称 (dev, prod, test)

    Returns:
        Dict[str, Any]: 配置字典

    Raises:
        FileNotFoundError: 配置文件不存在
        configparser.Error: INI 格式错误
    """
    # 获取项目根目录
    project_root = Path(__file__).parent.parent.parent
    config_file = project_root / "config" / f"{env}.ini"

    if not config_file.exists():
        raise FileNotFoundError(f"配置文件不存在: {config_file}")

    try:
        config = configparser.ConfigParser()
        config.read(config_file, encoding='utf-8')

        # 转换为字典格式
        config_data = {}

        for section_name in config.sections():
            section_data = {}
            for key, value in config[section_name].items():
                # 尝试转换数据类型
                section_data[key] = _convert_ini_value(value)
            config_data[section_name] = section_data

        # 添加环境标识
        config_data['env'] = env
        return config_data

    except configparser.Error as e:
        raise configparser.Error(f"配置文件 {config_file} INI 格式错误: {e}")


def _convert_ini_value(value: str) -> Union[str, int, float, bool, List[str]]:
    """
    转换 INI 配置值的数据类型

    Args:
        value: 原始字符串值

    Returns:
        转换后的值
    """
    # 布尔值
    if value.lower() in ('true', 'yes', '1', 'on'):
        return True
    elif value.lower() in ('false', 'no', '0', 'off'):
        return False

    # 列表 (逗号分隔)
    if ',' in value:
        return [item.strip() for item in value.split(',')]

    # 数字
    try:
        if '.' in value:
            return float(value)
        else:
            return int(value)
    except ValueError:
        pass

    # 字符串
    return value


def load_config_file(env: str = "dev", file_format: str = "auto") -> Dict[str, Any]:
    """
    从配置文件加载配置，自动检测文件格式

    Args:
        env: 环境名称 (dev, prod, test)
        file_format: 文件格式 (json, ini, auto)

    Returns:
        Dict[str, Any]: 配置字典

    Raises:
        FileNotFoundError: 配置文件不存在
        ValueError: 不支持的文件格式
    """
    project_root = Path(__file__).parent.parent.parent

    if file_format == "auto":
        # 优先尝试 JSON，然后 INI
        json_file = project_root / "config" / f"{env}.json"
        ini_file = project_root / "config" / f"{env}.ini"

        if json_file.exists():
            return load_config_from_json(env)
        elif ini_file.exists():
            return load_config_from_ini(env)
        else:
            raise FileNotFoundError(f"找不到环境 '{env}' 的配置文件 (支持 .json 和 .ini 格式)")

    elif file_format == "json":
        return load_config_from_json(env)
    elif file_format == "ini":
        return load_config_from_ini(env)
    else:
        raise ValueError(f"不支持的文件格式: {file_format}，支持的格式: json, ini, auto")


def create_settings_from_config(config_data: Dict[str, Any]) -> Settings:
    """
    从配置字典创建 Settings 实例

    Args:
        config_data: 配置字典

    Returns:
        Settings: 配置实例
    """
    return Settings(
        env=config_data.get('env', 'dev'),
        app=AppConfig(**config_data.get('app', {})),
        database=DatabaseConfig(**config_data.get('database', {})),
        redis=RedisConfig(**config_data.get('redis', {})),
        logging=LoggingConfig(**config_data.get('logging', {})),
        cors=CorsConfig(**config_data.get('cors', {})),
        upload=UploadConfig(**config_data.get('upload', {})),
        minio=MinioConfig(**config_data.get('minio', {}))
    )


def init_settings(env: str = "dev", file_format: str = "auto") -> Settings:
    """
    初始化配置系统

    Args:
        env: 环境名称 (dev, prod, test)
        file_format: 文件格式 (json, ini, auto)

    Returns:
        Settings: 配置实例

    Raises:
        FileNotFoundError: 配置文件不存在
        ValueError: 配置验证失败
    """
    # 验证环境参数
    valid_envs = ["dev", "prod", "test"]
    if env not in valid_envs:
        raise ValueError(f"不支持的环境 '{env}'，支持的环境: {valid_envs}")

    # 加载配置
    config_data = load_config_file(env, file_format)

    # 创建配置实例
    settings = create_settings_from_config(config_data)

    return settings


# 全局配置实例 (默认开发环境)
_global_settings: Optional[Settings] = None


def get_settings() -> Settings:
    """获取配置实例 - 用于依赖注入"""
    global _global_settings
    if _global_settings is None:
        _global_settings = init_settings("dev")
    return _global_settings


def set_global_settings(settings: Settings) -> None:
    """设置全局配置实例"""
    global _global_settings
    _global_settings = settings