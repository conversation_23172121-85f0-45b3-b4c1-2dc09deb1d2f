"""
配置管理系统
基于 Pydantic Settings 实现多环境配置管理
"""
from typing import Optional, List
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基础配置
    APP_NAME: str = "FastAPI OCR Project"
    APP_VERSION: str = "1.0.0"
    APP_ENV: str = Field(default="dev", description="应用环境: dev/test/prod")
    DEBUG: bool = Field(default=True, description="调试模式")
    
    # 服务器配置
    HOST: str = Field(default="0.0.0.0", description="服务器主机")
    PORT: int = Field(default=8000, description="服务器端口")
    
    # 数据库配置
    DATABASE_URL: Optional[str] = Field(default=None, description="数据库连接URL")
    DATABASE_ECHO: bool = Field(default=False, description="数据库SQL日志")
    DB_POOL_SIZE: int = Field(default=10, description="数据库连接池大小 (仅 MySQL)")
    DB_MAX_OVERFLOW: int = Field(default=20, description="连接池最大溢出 (仅 MySQL)")
    
    # Redis配置 (可选)
    REDIS_URL: Optional[str] = Field(default=None, description="Redis连接URL")
    
    # 日志配置
    LOG_LEVEL: str = Field(default="INFO", description="日志级别")
    LOG_FILE: Optional[str] = Field(default=None, description="日志文件路径")
    LOG_ROTATION: str = Field(default="1 day", description="日志轮转周期")
    LOG_RETENTION: str = Field(default="30 days", description="日志保留时间")
    
    # CORS配置
    CORS_ORIGINS: List[str] = Field(
        default=["*"],
        description="允许的跨域来源"
    )
    CORS_CREDENTIALS: bool = Field(default=True, description="允许携带凭证")
    CORS_METHODS: List[str] = Field(
        default=["*"],
        description="允许的HTTP方法"
    )
    CORS_HEADERS: List[str] = Field(
        default=["*"],
        description="允许的请求头"
    )
    
    # JWT配置 (预留)
    SECRET_KEY: str = Field(default="your-secret-key-here", description="JWT密钥")
    ALGORITHM: str = Field(default="HS256", description="JWT算法")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, description="访问令牌过期时间(分钟)")
    
    # 文件上传配置
    UPLOAD_DIR: str = Field(default="uploads", description="文件上传目录")
    MAX_FILE_SIZE: int = Field(default=10 * 1024 * 1024, description="最大文件大小(字节)")
    ALLOWED_EXTENSIONS: List[str] = Field(
        default=["jpg", "jpeg", "png", "gif", "pdf"],
        description="允许的文件扩展名"
    )
    
    # MinIO 对象存储配置
    MINIO_ENDPOINT: str = Field(default="localhost:9000", description="MinIO服务端点")
    MINIO_ACCESS_KEY: str = Field(default="minioadmin", description="MinIO访问密钥")
    MINIO_SECRET_KEY: str = Field(default="minioadmin", description="MinIO秘密密钥")
    MINIO_SECURE: bool = Field(default=False, description="是否使用HTTPS")
    MINIO_BUCKET_NAME: str = Field(default="ocr-images", description="存储桶名称")
    ENABLE_MINIO_STORAGE: bool = Field(default=True, description="是否启用MinIO存储")

    # 配置模型设置
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=True,
        extra="ignore"
    )
    
    @property
    def database_url(self) -> str:
        """获取数据库连接URL"""
        # 按环境优先返回默认数据库 URL
        if self.APP_ENV == "test":
            return "sqlite+aiosqlite:///./test.db"
        elif self.APP_ENV == "dev":
            return "sqlite+aiosqlite:///./dev.db"
        
        # 生产环境可使用自定义 DATABASE_URL，否则使用默认 MySQL
        if self.APP_ENV == "prod":
            if self.DATABASE_URL:
                return self.DATABASE_URL
            return "mysql+aiomysql://root:QWEasdZXC456456@***********:3306/ocr_data"
        
        # 其他未知环境回退到 SQLite
        if self.DATABASE_URL:
            return self.DATABASE_URL
            # 生产环境默认使用 MySQL
            return "mysql+aiomysql://root:QWEasdZXC456456@***********:3306/ocr_data"
        else:
            # 开发(dev) / 测试(test) 环境默认使用 SQLite
            db_name = "test.db" if self.APP_ENV == "test" else "dev.db"
            return f"sqlite+aiosqlite:///./{db_name}"
    
    @property
    def is_development(self) -> bool:
        """判断是否为开发环境"""
        return self.APP_ENV == "dev"
    
    @property
    def is_production(self) -> bool:
        """判断是否为生产环境"""
        return self.APP_ENV == "prod"
    
    @property
    def is_testing(self) -> bool:
        """判断是否为测试环境"""
        return self.APP_ENV == "test"


# 全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """获取配置实例 - 用于依赖注入"""
    return settings 