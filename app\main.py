"""
FastAPI 应用主文件 - 纯粹的应用逻辑
"""
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

from .config.settings import get_settings
from .config.database import init_db, close_db
from .core.logging import get_logger
from .api import api_v1_router

logger = get_logger(__name__)
settings = get_settings()


async def init_minio():
    """初始化 MinIO 客户端"""
    try:
        from .utils.minio_client import get_minio_client
        
        minio_client = get_minio_client()
        
        if minio_client.enabled:
            bucket_created = await minio_client.ensure_bucket_exists()
            if bucket_created:
                logger.info(f"MinIO 存储桶 '{minio_client.bucket_name}' 已准备就绪")
            else:
                logger.warning(f"MinIO 存储桶 '{minio_client.bucket_name}' 创建失败，但服务仍可继续运行")
        else:
            logger.info("MinIO 存储已禁用")
            
    except Exception as e:
        logger.error(f"MinIO 客户端初始化失败: {e}")
        logger.warning("MinIO 初始化失败，但应用将继续启动（文件存储功能可能受影响）")


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    logger.info("应用启动中...")
    try:
        await init_db()
        logger.info("数据库初始化完成")
        
        await init_minio()
        logger.info("MinIO 客户端初始化完成")
        
    except Exception as e:
        logger.error(f"应用初始化失败: {e}")
        raise
    
    yield
    
    logger.info("应用关闭中...")
    try:
        await close_db()
        logger.info("数据库连接已关闭")
    except Exception as e:
        logger.error(f"数据库关闭失败: {e}")


# 创建 FastAPI 应用实例
app = FastAPI(
    title=settings.app.name,
    version=settings.app.version,
    description="FastAPI OCR 项目 - 一个功能完整的 Web API 框架",
    docs_url="/docs" if not settings.is_production else None,
    redoc_url="/redoc" if not settings.is_production else None,
    openapi_url="/openapi.json" if not settings.is_production else None,
    lifespan=lifespan,
    debug=settings.app.debug,
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors.origins,
    allow_credentials=settings.cors.credentials,
    allow_methods=settings.cors.methods,
    allow_headers=settings.cors.headers,
)

# 集成API路由
app.include_router(api_v1_router, prefix="/api/v1")


# 全局异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理"""
    from .utils.response import internal_error_response, create_json_response
    
    request_id = getattr(request.state, 'request_id', 'unknown')
    
    logger.error(
        f"全局异常处理 [{request_id}] {type(exc).__name__}: {str(exc)}",
        extra={
            "request_id": request_id,
            "url": str(request.url),
            "method": request.method,
        },
        exc_info=True
    )
    
    message = "服务器内部错误" if not settings.app.debug else str(exc)
    response = internal_error_response(message=message)
    return create_json_response(response, 500)


# 应用信息相关API
@app.get("/", tags=["应用信息"])
async def root():
    """根路径欢迎信息"""
    from .utils.response import success_response
    
    data = {
        "app_name": settings.app.name,
        "version": settings.app.version,
        "environment": settings.env,
        "docs_url": "/docs" if not settings.is_production else "文档在生产环境中已禁用",
        "status": "运行中"
    }
    
    return success_response(
        data=data,
        message=f"欢迎使用 {settings.app.name}"
    )


@app.get("/health", tags=["应用信息"])
async def health_check():
    """健康检查端点"""
    from .config.database import check_db_health
    from .utils.response import success_response, error_response
    from datetime import datetime
    
    db_healthy = await check_db_health()
    
    data = {
        "status": "healthy" if db_healthy else "unhealthy",
        "database": "connected" if db_healthy else "disconnected",
        "version": settings.app.version,
        "environment": settings.env,
        "timestamp": datetime.utcnow().isoformat() + "Z"
    }
    
    if db_healthy:
        return success_response(data=data, message="系统健康状态正常")
    else:
        return error_response(data=data, message="系统健康状态异常", code=503)


@app.get("/info", tags=["应用信息"])
async def app_info():
    """应用信息端点"""
    from .utils.response import success_response
    
    data = {
        "app_name": settings.app.name,
        "version": settings.app.version,
        "environment": settings.env,
        "debug": settings.app.debug,
        "database_url": settings.database.url.split('@')[0] + '@***' if '@' in settings.database.url else settings.database.url,
        "cors_origins": settings.cors.origins,
        "upload_dir": settings.upload.dir,
        "max_file_size": settings.upload.max_file_size,
        "allowed_extensions": settings.upload.allowed_extensions,
    }
    
    return success_response(data=data, message="应用信息获取成功")
