"""
分页相关的数据模型和工具函数
支持基于偏移量的分页查询
"""
from typing import Optional, Any, List
from pydantic import BaseModel, Field, validator
from math import ceil


class PaginationParams(BaseModel):
    """
    分页查询参数模型
    用于接收前端传递的分页参数
    """
    page: int = Field(1, ge=1, description="页码，从1开始", example=1)
    size: int = Field(20, ge=1, le=100, description="每页大小，最大100", example=20)
    
    @validator('page')
    def validate_page(cls, v):
        """验证页码"""
        if v < 1:
            raise ValueError('页码必须大于0')
        return v
    
    @validator('size')
    def validate_size(cls, v):
        """验证每页大小"""
        if v < 1:
            raise ValueError('每页大小必须大于0')
        if v > 100:
            raise ValueError('每页大小不能超过100')
        return v
    
    @property
    def offset(self) -> int:
        """计算偏移量"""
        return (self.page - 1) * self.size
    
    @property
    def limit(self) -> int:
        """获取限制数量"""
        return self.size


class PaginationInfo(BaseModel):
    """
    分页信息模型
    包含分页的详细信息
    """
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
    total: int = Field(..., description="总记录数")
    pages: int = Field(..., description="总页数")
    has_prev: bool = Field(..., description="是否有上一页")
    has_next: bool = Field(..., description="是否有下一页")
    prev_page: Optional[int] = Field(None, description="上一页页码")
    next_page: Optional[int] = Field(None, description="下一页页码")
    
    class Config:
        """Pydantic配置"""
        json_schema_extra = {
            "example": {
                "page": 1,
                "size": 20,
                "total": 100,
                "pages": 5,
                "has_prev": False,
                "has_next": True,
                "prev_page": None,
                "next_page": 2
            }
        }


class PaginatedData(BaseModel):
    """
    分页数据模型
    包含数据列表和分页信息
    """
    items: List[Any] = Field(..., description="数据列表")
    pagination: PaginationInfo = Field(..., description="分页信息")
    
    class Config:
        """Pydantic配置"""
        json_schema_extra = {
            "example": {
                "items": [],
                "pagination": {
                    "page": 1,
                    "size": 20,
                    "total": 100,
                    "pages": 5,
                    "has_prev": False,
                    "has_next": True,
                    "prev_page": None,
                    "next_page": 2
                }
            }
        }


def create_pagination_info(
    page: int,
    size: int,
    total: int
) -> PaginationInfo:
    """
    创建分页信息
    
    Args:
        page: 当前页码
        size: 每页大小
        total: 总记录数
        
    Returns:
        PaginationInfo: 分页信息对象
    """
    pages = ceil(total / size) if total > 0 else 0
    has_prev = page > 1
    has_next = page < pages
    prev_page = page - 1 if has_prev else None
    next_page = page + 1 if has_next else None
    
    return PaginationInfo(
        page=page,
        size=size,
        total=total,
        pages=pages,
        has_prev=has_prev,
        has_next=has_next,
        prev_page=prev_page,
        next_page=next_page
    )


def create_paginated_data(
    items: List[Any],
    page: int,
    size: int,
    total: int
) -> PaginatedData:
    """
    创建分页数据
    
    Args:
        items: 数据列表
        page: 当前页码
        size: 每页大小
        total: 总记录数
        
    Returns:
        PaginatedData: 分页数据对象
    """
    pagination_info = create_pagination_info(page, size, total)
    
    return PaginatedData(
        items=items,
        pagination=pagination_info
    )


class SearchParams(BaseModel):
    """
    搜索参数模型
    结合分页参数使用
    """
    keyword: Optional[str] = Field(None, description="搜索关键词", example="")
    sort_by: Optional[str] = Field(None, description="排序字段", example="created_at")
    sort_order: Optional[str] = Field("desc", description="排序方向", example="desc")
    
    @validator('sort_order')
    def validate_sort_order(cls, v):
        """验证排序方向"""
        if v and v.lower() not in ['asc', 'desc']:
            raise ValueError('排序方向必须是asc或desc')
        return v.lower() if v else 'desc'
    
    class Config:
        """Pydantic配置"""
        json_schema_extra = {
            "example": {
                "keyword": "搜索关键词",
                "sort_by": "created_at",
                "sort_order": "desc"
            }
        }


class PaginatedSearchParams(PaginationParams):
    """
    分页搜索参数模型
    继承分页参数，添加搜索功能
    """
    keyword: Optional[str] = Field(None, description="搜索关键词", example="")
    sort_by: Optional[str] = Field(None, description="排序字段", example="created_at")
    sort_order: Optional[str] = Field("desc", description="排序方向", example="desc")
    
    @validator('sort_order')
    def validate_sort_order(cls, v):
        """验证排序方向"""
        if v and v.lower() not in ['asc', 'desc']:
            raise ValueError('排序方向必须是asc或desc')
        return v.lower() if v else 'desc' 