#!/usr/bin/env python3
"""
FastAPI OCR 项目 - 使用 typer 的统一启动入口
支持命令行参数和配置文件管理
"""
import os
import sys
from pathlib import Path
from typing_extensions import Annotated

import typer
import uvicorn

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from app.config.settings import init_settings, get_settings
from app.core.logging import get_logger

# 创建 typer 应用
cli_app = typer.Typer(
    name="fastapi-ocr",
    help="FastAPI OCR 项目命令行工具",
    add_completion=False
)

logger = get_logger(__name__)


@cli_app.command()
def serve(
    env: Annotated[str, typer.Option("--env", "-e", help="运行环境")] = "dev",
    host: Annotated[str, typer.Option("--host", help="服务器主机地址")] = None,
    port: Annotated[int, typer.Option("--port", "-p", help="服务器端口")] = None,
    workers: Annotated[int, typer.Option("--workers", "-w", help="工作进程数量")] = 1,
    reload: Annotated[bool, typer.Option("--reload", help="启用自动重载")] = None,
):
    """启动 FastAPI 应用服务器"""
    
    # 验证环境参数
    if env not in ["dev", "prod", "test"]:
        typer.echo(f"错误: 不支持的环境 '{env}'，支持的环境: dev, prod, test", err=True)
        raise typer.Exit(1)
    
    try:
        # 初始化配置
        settings = init_settings(env)
        typer.echo(f"✅ 配置加载成功: {env} 环境")
        
    except FileNotFoundError as e:
        typer.echo(f"❌ 配置文件错误: {e}", err=True)
        raise typer.Exit(1)
    except Exception as e:
        typer.echo(f"❌ 配置加载失败: {e}", err=True)
        raise typer.Exit(1)
    
    # 参数覆盖配置文件
    final_host = host or settings.app.host
    final_port = port or settings.app.port
    
    # 根据环境设置默认的 reload 行为
    if reload is None:
        reload = settings.is_development
    
    # 配置 uvicorn 参数
    uvicorn_config = {
        "app": "app.main:app",  # 指向 app/main.py 中的 app 实例
        "host": final_host,
        "port": final_port,
        "log_level": settings.logging.level.lower(),
    }
    
    # 根据环境配置不同的启动参数
    if settings.is_development:
        uvicorn_config.update({
            "reload": reload,
            "reload_dirs": ["app", "config"],
            "reload_excludes": ["*.pyc", "__pycache__", "logs", "uploads"],
        })
        typer.echo("🔧 开发模式启动 - 自动重载已启用")
    
    elif settings.is_production:
        uvicorn_config.update({
            "workers": workers,
            "access_log": True,
            "use_colors": False,
        })
        typer.echo(f"🚀 生产模式启动 - 工作进程数: {workers}")
    
    else:  # test environment
        uvicorn_config.update({
            "reload": False,
            "access_log": False,
        })
        typer.echo("🧪 测试模式启动")
    
    # 启动信息
    typer.echo(f"🌟 启动 {settings.app.name}")
    typer.echo(f"📦 版本: {settings.app.version}")
    typer.echo(f"🌍 环境: {env}")
    typer.echo(f"🔗 地址: http://{final_host}:{final_port}")
    typer.echo(f"📚 文档: http://{final_host}:{final_port}/docs")
    typer.echo(f"🗄️  数据库: {settings.database.url}")
    
    try:
        # 启动服务器
        uvicorn.run(**uvicorn_config)
    except KeyboardInterrupt:
        typer.echo("\n👋 服务器已停止")
    except Exception as e:
        typer.echo(f"❌ 服务器启动失败: {e}", err=True)
        raise typer.Exit(1)


@cli_app.command()
def config(
    env: Annotated[str, typer.Option("--env", "-e", help="查看指定环境配置")] = "dev",
):
    """查看配置信息"""
    try:
        settings = init_settings(env)
        
        typer.echo(f"📋 {env.upper()} 环境配置:")
        typer.echo(f"  应用名称: {settings.app.name}")
        typer.echo(f"  版本: {settings.app.version}")
        typer.echo(f"  调试模式: {settings.app.debug}")
        typer.echo(f"  服务地址: {settings.app.host}:{settings.app.port}")
        typer.echo(f"  数据库: {settings.database.url}")
        typer.echo(f"  日志级别: {settings.logging.level}")
        typer.echo(f"  MinIO存储: {'启用' if settings.minio.enabled else '禁用'}")
        
    except Exception as e:
        typer.echo(f"❌ 配置加载失败: {e}", err=True)
        raise typer.Exit(1)


@cli_app.command()
def init_db(
    env: Annotated[str, typer.Option("--env", "-e", help="环境")] = "dev",
):
    """初始化数据库"""
    import asyncio
    
    async def _init_db():
        try:
            settings = init_settings(env)
            typer.echo(f"🗄️  初始化 {env} 环境数据库...")
            
            from app.config.database import init_db
            await init_db()
            
            typer.echo("✅ 数据库初始化完成")
            
        except Exception as e:
            typer.echo(f"❌ 数据库初始化失败: {e}", err=True)
            raise typer.Exit(1)
    
    asyncio.run(_init_db())


@cli_app.command()
def version():
    """显示版本信息"""
    try:
        settings = get_settings()
        typer.echo(f"{settings.app.name} v{settings.app.version}")
    except:
        typer.echo("FastAPI OCR Project v1.0.0")


if __name__ == "__main__":
    cli_app() 
