"""
OCR任务相关的数据模型
用于API请求和响应的数据验证
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator


class OCRTaskBase(BaseModel):
    """
    OCR任务基础模型
    """
    company_name: str = Field(..., min_length=1, max_length=200, description="识别到的公司名称")
    table_data: List[Dict[str, Any]] = Field(..., description="提取的表格数据")
    table_rows: int = Field(..., ge=0, description="表格行数")
    field_names: List[str] = Field(..., description="字段名称数组")
    processing_time: float = Field(..., ge=0, description="处理耗时（秒）")
    confidence_threshold: float = Field(..., ge=0, le=1, description="置信度阈值")
    content_height: int = Field(..., ge=0, description="内容高度")
    image_uuid: str = Field(..., description="图片UUID")
    image_filename: str = Field(..., description="原始文件名")
    image_minio_path: str = Field(..., description="MinIO存储路径")
    image_upload_time: datetime = Field(..., description="图片上传时间")
    is_table_data_modified: bool = Field(False, description="表格数据是否被手动修改过")
    
    @validator('company_name')
    def validate_company_name(cls, v):
        """验证公司名称"""
        if not v or not v.strip():
            raise ValueError('公司名称不能为空')
        return v.strip()
    
    @validator('table_data')
    def validate_table_data(cls, v):
        """验证表格数据"""
        if v is None:
            raise ValueError('表格数据不能为空')
        return v
    
    @validator('field_names')
    def validate_field_names(cls, v):
        """验证字段名称"""
        if not v:
            raise ValueError('字段名称不能为空')
        return v


class OCRTaskCreate(OCRTaskBase):
    """
    创建OCR任务请求模型
    """
    pass


class OCRTaskUpdate(BaseModel):
    """
    更新OCR任务请求模型
    """
    company_name: Optional[str] = Field(None, min_length=1, max_length=200, description="识别到的公司名称")
    table_data: Optional[List[Dict[str, Any]]] = Field(None, description="提取的表格数据")
    table_rows: Optional[int] = Field(None, ge=0, description="表格行数")
    field_names: Optional[List[str]] = Field(None, description="字段名称数组")
    processing_time: Optional[float] = Field(None, ge=0, description="处理耗时（秒）")
    confidence_threshold: Optional[float] = Field(None, ge=0, le=1, description="置信度阈值")
    content_height: Optional[int] = Field(None, ge=0, description="内容高度")
    image_uuid: Optional[str] = Field(None, description="图片UUID")
    image_filename: Optional[str] = Field(None, description="原始文件名")
    image_minio_path: Optional[str] = Field(None, description="MinIO存储路径")
    image_upload_time: Optional[datetime] = Field(None, description="图片上传时间")
    
    @validator('company_name')
    def validate_company_name(cls, v):
        """验证公司名称"""
        if v is not None and (not v or not v.strip()):
            raise ValueError('公司名称不能为空')
        return v.strip() if v else v
    
    @validator('field_names')
    def validate_field_names(cls, v):
        """验证字段名称"""
        if v is not None and not v:
            raise ValueError('字段名称不能为空')
        return v


class OCRTaskResponse(OCRTaskBase):
    """
    OCR任务响应模型
    """
    id: int = Field(..., description="OCR任务ID")
    created_at: Optional[str] = Field(None, description="创建时间")
    updated_at: Optional[str] = Field(None, description="更新时间")
    is_deleted: bool = Field(False, description="是否已删除")
    image_access_url: Optional[str] = Field(None, description="图片完整访问链接")
    
    class Config:
        json_schema_extra = {
            "example": {
                "id": 1,
                "company_name": "深圳市恒嘉通实业有限公司",
                "table_data": [
                    {
                        "row_index": 0,
                        "订单号码": "I262406280002"
                    }
                ],
                "table_rows": 1,
                "field_names": ["订单号码"],
                "processing_time": 26.7,
                "confidence_threshold": 0.7,
                "content_height": 68,
                "image_uuid": "995fdabc-2651-4067-9df5-fb4307fd1698",
                "image_filename": "微信图片_20250715104131.jpg",
                "image_minio_path": "ocr-tasks/995fdabc-2651-4067-9df5-fb4307fd1698_微信图片_20250715104131.jpg",
                "image_upload_time": "2025-07-16T09:03:56.652171",
                "is_table_data_modified": False,
                "created_at": "2025-07-16T09:03:56.652171",
                "updated_at": "2025-07-16T09:03:56.652171",
                "is_deleted": False,
                "image_access_url": "https://minio.example.com/ocr/ocr-tasks/995fdabc-2651-4067-9df5-fb4307fd1698_微信图片_20250715104131.jpg?X-Amz-Algorithm=..."
            }
        }


class OCRTaskListItem(BaseModel):
    """
    OCR任务列表项模型
    """
    id: int = Field(..., description="OCR任务ID")
    company_name: str = Field(..., description="识别到的公司名称")
    table_rows: int = Field(..., description="表格行数")
    field_names: List[str] = Field(..., description="字段名称数组")
    processing_time: float = Field(..., description="处理耗时（秒）")
    image_filename: str = Field(..., description="原始文件名")
    is_table_data_modified: bool = Field(False, description="表格数据是否被手动修改过")
    created_at: Optional[str] = Field(None, description="创建时间")
    updated_at: Optional[str] = Field(None, description="更新时间")
    image_access_url: Optional[str] = Field(None, description="图片完整访问链接")
    
    class Config:
        json_schema_extra = {
            "example": {
                "id": 1,
                "company_name": "深圳市恒嘉通实业有限公司",
                "table_rows": 1,
                "field_names": ["订单号码"],
                "processing_time": 26.7,
                "image_filename": "微信图片_20250715104131.jpg",
                "is_table_data_modified": False,
                "created_at": "2025-07-16T09:03:56.652171",
                "updated_at": "2025-07-16T09:03:56.652171",
                "image_access_url": "https://minio.example.com/ocr/ocr-tasks/995fdabc-2651-4067-9df5-fb4307fd1698_微信图片_20250715104131.jpg?X-Amz-Algorithm=..."
            }
        }