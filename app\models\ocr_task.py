"""
OCR任务数据库模型
用于存储OCR识别任务的结果信息
"""
from typing import Optional, List, Dict, Any
from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, JSON, DateTime, Float, Boolean
from sqlalchemy.orm import validates

from .base import BaseModel


class OCRTask(BaseModel):
    """
    OCR任务数据模型
    
    存储OCR识别任务的结果信息，包括：
    - 公司信息
    - 表格数据
    - 处理统计信息
    - 图片信息
    """
    
    __tablename__ = "ocr_tasks"
    
    # 公司信息
    company_name = Column(
        String(200), 
        nullable=False, 
        index=True,
        comment="识别到的公司名称"
    )
    
    # 表格数据
    table_data = Column(
        JSON, 
        nullable=False,
        comment="提取的表格数据，JSON格式存储"
    )
    
    # 统计信息
    table_rows = Column(
        Integer, 
        nullable=False,
        comment="表格行数"
    )
    
    field_names = Column(
        JSON, 
        nullable=False,
        comment="字段名称数组"
    )
    
    processing_time = Column(
        Float, 
        nullable=False,
        comment="处理耗时（秒）"
    )
    
    confidence_threshold = Column(
        Float, 
        nullable=False,
        comment="置信度阈值"
    )
    
    content_height = Column(
        Integer, 
        nullable=False,
        comment="内容高度"
    )
    
    # 图片相关字段
    image_uuid = Column(
        String(36),
        nullable=False,
        comment="图片UUID"
    )
    
    image_filename = Column(
        String(255),
        nullable=False,
        comment="原始文件名"
    )
    
    image_minio_path = Column(
        String(500),
        nullable=False,
        comment="MinIO存储路径"
    )
    
    image_upload_time = Column(
        DateTime,
        nullable=False,
        comment="图片上传时间"
    )
    
    # 数据修改标记
    is_table_data_modified = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="表格数据是否被手动修改过"
    )
    
    @validates('company_name')
    def validate_company_name(self, key, value):
        """验证公司名称"""
        if not value or not value.strip():
            raise ValueError("公司名称不能为空")
        if len(value.strip()) > 200:
            raise ValueError("公司名称长度不能超过200个字符")
        return value.strip()
    
    @validates('table_rows')
    def validate_table_rows(self, key, value):
        """验证表格行数"""
        if value is None:
            raise ValueError("表格行数不能为空")
        if not isinstance(value, int) or value < 0:
            raise ValueError("表格行数必须是非负整数")
        return value
    
    @validates('processing_time', 'confidence_threshold')
    def validate_float_fields(self, key, value):
        """验证浮点数字段"""
        if value is None:
            raise ValueError(f"{key}不能为空")
        if not isinstance(value, (int, float)):
            raise ValueError(f"{key}必须是数字")
        return float(value)
    
    @validates('content_height')
    def validate_content_height(self, key, value):
        """验证内容高度"""
        if value is None:
            raise ValueError("内容高度不能为空")
        if not isinstance(value, int) or value < 0:
            raise ValueError("内容高度必须是非负整数")
        return value
    
    @validates('table_data', 'field_names')
    def validate_json_fields(self, key, value):
        """验证JSON字段"""
        if value is None:
            raise ValueError(f"{key}不能为空")
        return value
    
    @validates('image_uuid', 'image_filename', 'image_minio_path')
    def validate_string_fields(self, key, value):
        """验证字符串字段"""
        if not value or not value.strip():
            raise ValueError(f"{key}不能为空")
        return value.strip()
    
    def __repr__(self):
        """字符串表示"""
        return f"<OCRTask(id={self.id}, company_name='{self.company_name}', table_rows={self.table_rows})>"
    
    def to_dict_summary(self) -> Dict[str, Any]:
        """
        转换为摘要字典（用于列表显示）
        
        Returns:
            dict: 包含基本信息的字典
        """
        return {
            "id": self.id,
            "company_name": self.company_name,
            "table_rows": self.table_rows,
            "field_names": self.field_names,
            "processing_time": self.processing_time,
            "image_filename": self.image_filename,
            "is_table_data_modified": self.is_table_data_modified,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
    
    def to_dict_detail(self) -> Dict[str, Any]:
        """
        转换为详细字典（用于详情显示）
        
        Returns:
            dict: 包含所有信息的字典
        """
        return {
            "id": self.id,
            "company_name": self.company_name,
            "table_data": self.table_data,
            "table_rows": self.table_rows,
            "field_names": self.field_names,
            "processing_time": self.processing_time,
            "confidence_threshold": self.confidence_threshold,
            "content_height": self.content_height,
            "image_uuid": self.image_uuid,
            "image_filename": self.image_filename,
            "image_minio_path": self.image_minio_path,
            "image_upload_time": self.image_upload_time.isoformat() if self.image_upload_time else None,
            "is_table_data_modified": self.is_table_data_modified,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "is_deleted": self.is_deleted,
        }
    
    @classmethod
    def create_from_dict(cls, data: Dict[str, Any]) -> 'OCRTask':
        """
        从字典创建OCR任务实例
        
        Args:
            data: 包含OCR任务信息的字典
            
        Returns:
            OCRTask: OCR任务实例
        """
        return cls(
            company_name=data.get('company_name'),
            table_data=data.get('table_data', []),
            table_rows=data.get('table_rows', 0),
            field_names=data.get('field_names', []),
            processing_time=data.get('processing_time', 0.0),
            confidence_threshold=data.get('confidence_threshold', 0.7),
            content_height=data.get('content_height', 0),
            image_uuid=data.get('image_uuid'),
            image_filename=data.get('image_filename'),
            image_minio_path=data.get('image_minio_path'),
            image_upload_time=data.get('image_upload_time'),
            is_table_data_modified=data.get('is_table_data_modified', False)
        )
    
    def update_from_dict(self, data: Dict[str, Any]) -> None:
        """
        从字典更新OCR任务信息
        
        Args:
            data: 包含更新信息的字典
        """
        updateable_fields = [
            'company_name', 'table_data', 'table_rows', 'field_names',
            'processing_time', 'confidence_threshold', 'content_height',
            'image_uuid', 'image_filename', 'image_minio_path', 'image_upload_time',
            'is_table_data_modified'
        ]
        
        for field in updateable_fields:
            if field in data and data[field] is not None:
                setattr(self, field, data[field])