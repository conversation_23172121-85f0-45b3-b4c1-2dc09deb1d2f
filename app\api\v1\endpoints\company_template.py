"""
公司模版管理API端点
提供公司模版的CRUD操作接口
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path, UploadFile, File, Form
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_
from sqlalchemy.orm import selectinload

from app.config.database import get_db
from app.models.company_template import CompanyTemplate
from app.schemas.company_template import (
    CompanyTemplateCreate,
    CompanyTemplateCreateWithOCR,
    CompanyTemplateUpdate,
    CompanyTemplateResponse,
    CompanyTemplateListItem
)
from app.schemas.response import ApiResponse, StatusCode, ResponseMessage
from app.schemas.pagination import PaginationParams, create_paginated_data
from app.core.logging import get_logger
from app.utils.file_handler import FileHandler
from app.utils.ocr_processor import perform_ocr_recognition
from app.utils.text_matcher import calculate_padding_from_ocr_and_annotations
from app.utils.minio_client import get_image_url
import json

logger = get_logger(__name__)

router = APIRouter()

# 文件处理器实例
file_handler = FileHandler()


@router.get(
    "/",
    response_model=ApiResponse,
    summary="获取公司模版列表",
    description="获取公司模版信息列表，支持分页和搜索"
)
async def get_company_templates(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页大小"),
    keyword: Optional[str] = Query(None, description="搜索关键词（公司名称）"),
    db: AsyncSession = Depends(get_db)
):
    """
    获取公司模版列表
    
    Args:
        page: 页码，从1开始
        size: 每页大小，最大100
        keyword: 搜索关键词，用于搜索公司名称
        db: 数据库会话
        
    Returns:
        ApiResponse: 包含公司模版列表的响应
    """
    try:
        # 构建基础查询
        query = select(CompanyTemplate).where(CompanyTemplate.is_deleted == False)
        
        # 添加搜索条件
        if keyword:
            query = query.where(CompanyTemplate.company_name.contains(keyword))
        
        # 获取总数
        count_query = select(func.count(CompanyTemplate.id)).where(
            and_(
                CompanyTemplate.is_deleted == False,
                CompanyTemplate.company_name.contains(keyword) if keyword else True
            )
        )
        total_result = await db.execute(count_query)
        total = total_result.scalar() or 0
        
        # 分页查询
        offset = (page - 1) * size
        query = query.order_by(CompanyTemplate.created_at.desc()).offset(offset).limit(size)
        
        result = await db.execute(query)
        templates = result.scalars().all()
        
        # 转换为响应模型
        template_list = []
        for template in templates:
            template_data = template.to_dict_summary()
            
            # 如果存在MinIO路径，生成完整的访问链接
            if template_data.get('image_minio_path'):
                try:
                    image_url = await get_image_url(template_data['image_minio_path'])
                    if image_url:
                        template_data['image_access_url'] = image_url
                except Exception as e:
                    logger.error(f"生成图片访问链接异常 (模板ID: {template.id}): {e}")
            
            template_list.append(CompanyTemplateListItem(**template_data))
        
        # 创建分页数据
        paginated_data = create_paginated_data(
            items=template_list,
            page=page,
            size=size,
            total=total
        )
        
        logger.info(f"获取公司模版列表成功，返回{len(template_list)}条记录")
        
        return ApiResponse(
            code=StatusCode.SUCCESS,
            message=ResponseMessage.SUCCESS,
            data=paginated_data.dict()
        )
        
    except Exception as e:
        logger.error(f"获取公司模版列表失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取公司模版列表失败: {str(e)}"
        )


@router.get(
    "/{template_id}",
    response_model=ApiResponse[CompanyTemplateResponse],
    summary="获取公司模版详情",
    description="根据ID获取单个公司模版的详细信息"
)
async def get_company_template(
    template_id: int = Path(..., description="公司模版ID"),
    db: AsyncSession = Depends(get_db)
):
    """
    获取公司模版详情
    
    Args:
        template_id: 公司模版ID
        db: 数据库会话
        
    Returns:
        ApiResponse: 包含公司模版详细信息的响应
    """
    try:
        # 查询模版
        query = select(CompanyTemplate).where(
            and_(
                CompanyTemplate.id == template_id,
                CompanyTemplate.is_deleted == False
            )
        )
        
        result = await db.execute(query)
        template = result.scalar_one_or_none()
        
        if not template:
            logger.warning(f"公司模版不存在: {template_id}")
            raise HTTPException(
                status_code=404,
                detail="公司模版不存在"
            )
        
        # 转换为响应模型
        template_data = template.to_dict_detail()
        
        # 如果存在MinIO路径，生成完整的访问链接
        if template_data.get('image_minio_path'):
            try:
                image_url = await get_image_url(template_data['image_minio_path'])
                if image_url:
                    template_data['image_access_url'] = image_url
                    logger.info(f"生成图片访问链接成功: {template_id}")
                else:
                    logger.warning(f"生成图片访问链接失败: {template_id}")
            except Exception as e:
                logger.error(f"生成图片访问链接异常: {e}")
        
        template_response = CompanyTemplateResponse(**template_data)
        
        logger.info(f"获取公司模版详情成功: {template_id}")
        
        return ApiResponse(
            code=StatusCode.SUCCESS,
            message=ResponseMessage.SUCCESS,
            data=template_response
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取公司模版详情失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取公司模版详情失败: {str(e)}"
        )



@router.post(
    "/create-or-update-with-ocr",
    response_model=ApiResponse[CompanyTemplateResponse],
    summary="创建或更新公司模版（支持OCR计算）",
    description="上传图片并创建或更新公司模版，自动使用OCR计算padding值。如果提供ID且存在则更新，否则创建新模版"
)
async def create_or_update_company_template_with_ocr(
    company_name: str = Form(..., description="公司名称"),
    document_image: UploadFile = File(..., description="公司单据图片文件"),
    annotations: str = Form(..., description="标注信息数组（JSON格式）"),
    confidence_threshold: float = Form(0.7, description="OCR置信度阈值"),
    id: Optional[int] = Form(None, description="模版ID，如果提供且存在则更新，否则创建新模版"),
    db: AsyncSession = Depends(get_db)
):
    """
    创建或更新公司模版（支持OCR计算）
    
    功能流程：
    1. 检查是否提供了ID，如果提供则尝试更新，否则创建
    2. 接收上传的图片文件和基础信息
    3. 从annotations中提取extract_fields和content_height
    4. 对图片进行OCR识别
    5. 根据annotations中的字段名称进行匹配
    6. 计算padding值
    7. 创建或更新公司模版记录
    
    Args:
        company_name: 公司名称
        document_image: 上传的图片文件
        annotations: 标注信息数组（JSON字符串）
        confidence_threshold: OCR置信度阈值
        id: 模版ID，如果提供且存在则更新，否则创建新模版
        db: 数据库会话
        
    Returns:
        ApiResponse: 包含创建或更新的公司模版信息的响应
    """
    try:
        # 1. 解析annotations JSON字符串
        try:
            annotations_list = json.loads(annotations)
        except json.JSONDecodeError as e:
            logger.error(f"annotations JSON解析失败: {e}")
            raise HTTPException(
                status_code=400,
                detail=f"annotations JSON格式错误: {str(e)}"
            )
        
        # 2. 验证必要字段
        if not company_name or not company_name.strip():
            raise HTTPException(
                status_code=400,
                detail="公司名称不能为空"
            )
        
        if not annotations_list:
            raise HTTPException(
                status_code=400,
                detail="标注信息数组不能为空"
            )
        
        # 3. 检查是否为更新操作
        existing_template = None
        is_update = False
        
        if id is not None:
            # 查询现有模版
            query = select(CompanyTemplate).where(
                and_(
                    CompanyTemplate.id == id,
                    CompanyTemplate.is_deleted == False
                )
            )
            result = await db.execute(query)
            existing_template = result.scalar_one_or_none()
            
            if existing_template:
                is_update = True
                logger.info(f"找到现有模版，将进行更新操作: {id}")
            else:
                logger.warning(f"提供的ID {id} 不存在，将创建新模版")
        
        # 4. 从annotations中提取extract_fields和content_height
        extract_fields_list = []
        content_height = 0
        
        for annotation in annotations_list:
            # 验证annotation结构
            if not isinstance(annotation, dict):
                raise HTTPException(
                    status_code=400,
                    detail="annotations中的每个项目必须是对象"
                )
            
            annotation_type = annotation.get('type', '').lower()
            annotation_name = annotation.get('name', '').strip()
            annotation_height = annotation.get('height', 0)
            
            # 提取header类型的字段名 (支持'header'和'headr'两种拼写)
            if annotation_type in ['header', 'headr']:
                if annotation_name:
                    extract_fields_list.append(annotation_name)
                else:
                    logger.warning(f"发现没有name字段的header类型annotation: {annotation}")
            
            # 提取content类型的高度
            elif annotation_type == 'content':
                if annotation_height > 0:
                    content_height = max(content_height, annotation_height)
                else:
                    logger.warning(f"发现高度为0的content类型annotation: {annotation}")
        
        # 5. 验证提取的数据
        if not extract_fields_list:
            raise HTTPException(
                status_code=400,
                detail="annotations中缺少type为'header'的项目，无法提取字段信息"
            )
        
        if content_height == 0:
            raise HTTPException(
                status_code=400,
                detail="annotations中缺少type为'content'的项目，或者content高度为0"
            )
        
        logger.info(f"从annotations中提取到字段: {extract_fields_list}, 内容高度: {content_height}")
        
        # 6. 检查公司名称冲突（仅在创建或更新时名称发生变化时检查）
        if not is_update or (is_update and existing_template.company_name != company_name.strip()):
            existing_name_query = select(CompanyTemplate).where(
                and_(
                    CompanyTemplate.company_name == company_name.strip(),
                    CompanyTemplate.is_deleted == False,
                    CompanyTemplate.id != id if id else True
                )
            )
            
            existing_name_result = await db.execute(existing_name_query)
            existing_name_template = existing_name_result.scalar_one_or_none()
            
            if existing_name_template:
                logger.warning(f"公司模版名称已存在: {company_name}")
                raise HTTPException(
                    status_code=409,
                    detail="公司模版名称已存在"
                )
        
        # 7. 加载图片并进行OCR识别
        try:
            image = await file_handler.load_image_from_upload(document_image)
            logger.info(f"图片加载成功: {document_image.filename}")
        except Exception as e:
            logger.error(f"图片加载失败: {e}")
            raise HTTPException(
                status_code=400,
                detail=f"图片加载失败: {str(e)}"
            )
        
        # 8. 进行OCR识别
        try:
            image_result = await perform_ocr_recognition(image)
            if not image_result or not image_result.text_lines:
                raise HTTPException(
                    status_code=400,
                    detail="OCR识别失败或图片中未识别到文本"
                )
            logger.info(f"OCR识别成功，识别到 {len(image_result.text_lines)} 个文本行")
        except Exception as e:
            logger.error(f"OCR识别失败: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"OCR识别失败: {str(e)}"
            )
        
        # 9. 计算padding值并添加到annotations中
        try:
            modified_annotations = calculate_padding_from_ocr_and_annotations(
                image_result=image_result,
                annotations=annotations_list,
                confidence_threshold=confidence_threshold
            )
            logger.info(f"Padding计算完成，annotations已更新")
        except Exception as e:
            logger.error(f"Padding计算失败: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Padding计算失败: {str(e)}"
            )
        
        # 10. 保存图片到 MinIO
        image_info = None
        try:
            image_info = await file_handler.save_image_to_minio(
                document_image, 
                folder="company-templates"
            )
            if image_info:
                logger.info(f"图片保存到 MinIO 成功: {image_info[1]}")
            else:
                logger.warning("图片保存到 MinIO 失败，继续处理模板")
        except Exception as e:
            logger.error(f"保存图片到 MinIO 异常: {e}")
        
        # 11. 准备模版数据
        template_data = {
            'company_name': company_name.strip(),
            'document_image': f"/uploads/{document_image.filename}",  # 保持原有逻辑
            'extract_fields': extract_fields_list,
            'content_height': content_height,
            'annotations': modified_annotations  # 使用包含padding信息的annotations
        }
        
        # 添加图片信息（如果保存成功）
        if image_info:
            image_uuid, minio_path, original_filename, upload_time = image_info
            template_data.update({
                'image_uuid': image_uuid,
                'image_filename': original_filename,
                'image_minio_path': minio_path,
                'image_upload_time': upload_time
            })
        
        # 12. 创建或更新数据库记录
        if is_update:
            # 更新现有模版
            existing_template.update_from_dict(template_data)
            await db.commit()
            await db.refresh(existing_template)
            
            template_response_data = existing_template.to_dict_detail()
            template_response = CompanyTemplateResponse(**template_response_data)
            
            logger.info(f"更新公司模版成功: {existing_template.id}, 提取字段: {extract_fields_list}, "
                       f"内容高度: {content_height}, annotations已包含padding信息")
            
            return ApiResponse(
                code=StatusCode.SUCCESS,
                message=ResponseMessage.UPDATED,
                data=template_response
            )
        else:
            # 创建新模版
            new_template = CompanyTemplate.create_from_dict(template_data)
            
            db.add(new_template)
            await db.commit()
            await db.refresh(new_template)
            
            template_response_data = new_template.to_dict_detail()
            template_response = CompanyTemplateResponse(**template_response_data)
            
            logger.info(f"创建公司模版成功: {new_template.id}, 提取字段: {extract_fields_list}, "
                       f"内容高度: {content_height}, annotations已包含padding信息")
            
            return ApiResponse(
                code=StatusCode.CREATED,
                message=ResponseMessage.CREATED,
                data=template_response
            )
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"创建或更新公司模版失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"创建或更新公司模版失败: {str(e)}"
        )




@router.delete(
    "/{template_id}",
    response_model=ApiResponse[None],
    summary="删除公司模版",
    description="删除指定ID的公司模版（软删除）"
)
async def delete_company_template(
    template_id: int = Path(..., description="公司模版ID"),
    db: AsyncSession = Depends(get_db)
):
    """
    删除公司模版
    
    Args:
        template_id: 公司模版ID
        db: 数据库会话
        
    Returns:
        ApiResponse: 删除结果响应
    """
    try:
        # 查询要删除的模版
        query = select(CompanyTemplate).where(
            and_(
                CompanyTemplate.id == template_id,
                CompanyTemplate.is_deleted == False
            )
        )
        
        result = await db.execute(query)
        template = result.scalar_one_or_none()
        
        if not template:
            logger.warning(f"公司模版不存在: {template_id}")
            raise HTTPException(
                status_code=404,
                detail="公司模版不存在"
            )
        
        # 执行软删除
        template.soft_delete()
        
        await db.commit()
        
        logger.info(f"删除公司模版成功: {template_id}")
        
        return ApiResponse(
            code=StatusCode.SUCCESS,
            message=ResponseMessage.DELETED,
            data=None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"删除公司模版失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"删除公司模版失败: {str(e)}"
        ) 