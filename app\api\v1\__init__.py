"""
API v1 路由集成
"""
from fastapi import APIRouter

from .endpoints.company_template import router as company_template_router
from .endpoints.ocr_scan import router as ocr_scan_router
from .endpoints.image_access import router as image_access_router

# 创建API v1主路由器
api_v1_router = APIRouter()

# 集成各个端点路由
api_v1_router.include_router(
    company_template_router,
    prefix="/company-templates",
    tags=["公司模版管理"]
)

api_v1_router.include_router(
    ocr_scan_router,
    prefix="/ocr-scan",
    tags=["OCR扫描"]
)

api_v1_router.include_router(
    image_access_router,
    prefix="/images",
    tags=["图片访问"]
)

__all__ = ["api_v1_router"] 