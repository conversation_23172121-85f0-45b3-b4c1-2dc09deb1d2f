"""
数据模型基类
包含统一的时间戳字段、软删除支持和通用方法
"""
from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy import Column, Integer, DateTime, Boolean, String, Text
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.sql import func

from ..config.database import Base


class TimestampMixin:
    """时间戳混合类"""
    
    @declared_attr
    def created_at(cls):
        """创建时间"""
        return Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")
    
    @declared_attr
    def updated_at(cls):
        """更新时间"""
        return Column(
            DateTime, 
            default=datetime.utcnow, 
            onupdate=datetime.utcnow, 
            nullable=False,
            comment="更新时间"
        )


class SoftDeleteMixin:
    """软删除混合类"""
    
    @declared_attr
    def is_deleted(cls):
        """是否已删除"""
        return Column(<PERSON><PERSON><PERSON>, default=False, nullable=False, comment="是否已删除")
    
    @declared_attr
    def deleted_at(cls):
        """删除时间"""
        return Column(DateTime, nullable=True, comment="删除时间")


class BaseModel(Base, TimestampMixin, SoftDeleteMixin):
    """数据模型基类"""
    
    __abstract__ = True
    
    id = Column(Integer, primary_key=True, index=True, comment="主键ID")
    
    def to_dict(self, exclude_fields: Optional[list] = None) -> Dict[str, Any]:
        """
        将模型转换为字典
        
        Args:
            exclude_fields: 需要排除的字段列表
            
        Returns:
            dict: 模型字典表示
        """
        exclude_fields = exclude_fields or []
        result = {}
        
        for column in self.__table__.columns:
            if column.name not in exclude_fields:
                value = getattr(self, column.name)
                # 处理日期时间类型
                if isinstance(value, datetime):
                    value = value.isoformat()
                result[column.name] = value
        
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        """
        从字典创建模型实例
        
        Args:
            data: 数据字典
            
        Returns:
            BaseModel: 模型实例
        """
        # 过滤掉不存在的字段
        filtered_data = {}
        for key, value in data.items():
            if hasattr(cls, key):
                filtered_data[key] = value
        
        return cls(**filtered_data)
    
    def update_from_dict(self, data: Dict[str, Any], exclude_fields: Optional[list] = None):
        """
        从字典更新模型
        
        Args:
            data: 数据字典
            exclude_fields: 需要排除的字段列表
        """
        exclude_fields = exclude_fields or ['id', 'created_at']
        
        for key, value in data.items():
            if key not in exclude_fields and hasattr(self, key):
                setattr(self, key, value)
    
    def soft_delete(self):
        """软删除"""
        self.is_deleted = True
        self.deleted_at = datetime.utcnow()
    
    def restore(self):
        """恢复软删除"""
        self.is_deleted = False
        self.deleted_at = None
    
    def __repr__(self):
        """字符串表示"""
        return f"<{self.__class__.__name__}(id={self.id})>"


class AuditMixin:
    """审计混合类"""
    
    @declared_attr
    def created_by(cls):
        """创建者"""
        return Column(String(50), nullable=True, comment="创建者")
    
    @declared_attr
    def updated_by(cls):
        """更新者"""
        return Column(String(50), nullable=True, comment="更新者")
    
    @declared_attr
    def version(cls):
        """版本号"""
        return Column(Integer, default=1, nullable=False, comment="版本号")


class LogMixin:
    """日志混合类"""
    
    @declared_attr
    def operation_log(cls):
        """操作日志"""
        return Column(Text, nullable=True, comment="操作日志")
    
    def add_log(self, message: str, user: Optional[str] = None):
        """添加操作日志"""
        timestamp = datetime.utcnow().isoformat()
        log_entry = f"[{timestamp}] {user or 'System'}: {message}"
        
        if self.operation_log:
            self.operation_log += f"\n{log_entry}"
        else:
            self.operation_log = log_entry


class FullAuditModel(Base, TimestampMixin, SoftDeleteMixin, AuditMixin, LogMixin):
    """完整审计模型基类"""
    
    __abstract__ = True
    
    id = Column(Integer, primary_key=True, index=True, comment="主键ID")
    
    def to_dict(self, exclude_fields: Optional[list] = None) -> Dict[str, Any]:
        """
        将模型转换为字典
        
        Args:
            exclude_fields: 需要排除的字段列表
            
        Returns:
            dict: 模型字典表示
        """
        exclude_fields = exclude_fields or []
        result = {}
        
        for column in self.__table__.columns:
            if column.name not in exclude_fields:
                value = getattr(self, column.name)
                # 处理日期时间类型
                if isinstance(value, datetime):
                    value = value.isoformat()
                result[column.name] = value
        
        return result
    
    def soft_delete(self, user: Optional[str] = None):
        """软删除"""
        self.is_deleted = True
        self.deleted_at = datetime.utcnow()
        if user:
            self.updated_by = user
            self.add_log(f"软删除记录", user)
    
    def restore(self, user: Optional[str] = None):
        """恢复软删除"""
        self.is_deleted = False
        self.deleted_at = None
        if user:
            self.updated_by = user
            self.add_log(f"恢复删除记录", user)
    
    def __repr__(self):
        """字符串表示"""
        return f"<{self.__class__.__name__}(id={self.id})>" 