"""
公司服务模块
提供公司相关的数据库操作功能
"""
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.models.company_template import CompanyTemplate
from app.core.logging import get_logger

logger = get_logger(__name__)


async def get_all_company_names(db: AsyncSession) -> List[str]:
    """
    获取数据库中所有公司名称
    
    Args:
        db: 数据库会话
        
    Returns:
        List[str]: 公司名称列表
    """
    try:
        # 查询所有未删除的公司模版
        query = select(CompanyTemplate.company_name).where(
            CompanyTemplate.is_deleted == False
        )
        result = await db.execute(query)
        company_names = [row[0] for row in result.fetchall()]
        
        logger.info(f"获取到 {len(company_names)} 个公司名称")
        return company_names
        
    except Exception as e:
        logger.error(f"获取公司名称失败: {e}")
        raise


async def get_company_by_name(db: AsyncSession, company_name: str) -> Optional[CompanyTemplate]:
    """
    根据公司名称获取公司完整信息
    
    Args:
        db: 数据库会话
        company_name: 公司名称
        
    Returns:
        Optional[CompanyTemplate]: 公司模版对象
    """
    try:
        query = select(CompanyTemplate).where(
            CompanyTemplate.company_name == company_name,
            CompanyTemplate.is_deleted == False
        )
        result = await db.execute(query)
        company = result.scalars().first()
        
        if company:
            logger.info(f"找到公司信息: {company_name}")
        else:
            logger.warning(f"未找到公司信息: {company_name}")
            
        return company
        
    except Exception as e:
        logger.error(f"查询公司信息失败: {e}")
        raise


async def get_company_template_by_id(db: AsyncSession, company_id: int) -> Optional[CompanyTemplate]:
    """
    根据公司ID获取公司模版
    
    Args:
        db: 数据库会话
        company_id: 公司ID
        
    Returns:
        Optional[CompanyTemplate]: 公司模版对象
    """
    try:
        query = select(CompanyTemplate).where(
            CompanyTemplate.id == company_id,
            CompanyTemplate.is_deleted == False
        )
        result = await db.execute(query)
        return result.scalars().first()
        
    except Exception as e:
        logger.error(f"获取公司模版失败: {e}")
        raise 