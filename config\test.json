{"app": {"name": "FastAPI OCR Project", "version": "1.0.0", "debug": true, "host": "127.0.0.1", "port": 8001}, "database": {"url": "sqlite+aiosqlite:///./test.db", "echo": false, "pool_size": 5, "max_overflow": 10}, "redis": {"url": "redis://localhost:6379/1"}, "logging": {"level": "DEBUG", "rotation": "1 day", "retention": "7 days"}, "cors": {"origins": ["http://localhost:3000", "http://127.0.0.1:3000"], "credentials": true, "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "headers": ["*"]}, "upload": {"dir": "test_uploads", "max_file_size": 5242880, "allowed_extensions": ["jpg", "jpeg", "png"]}, "minio": {"endpoint": "localhost:9000", "access_key": "testuser", "secret_key": "testpass", "secure": false, "bucket_name": "test-ocr", "enabled": false}}