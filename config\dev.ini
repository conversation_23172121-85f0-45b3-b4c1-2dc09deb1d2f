[app]
name = FastAPI OCR Project
version = 1.0.0
debug = true
host = 0.0.0.0
port = 8000

[database]
url = sqlite+aiosqlite:///./dev.db
echo = true
pool_size = 10
max_overflow = 20

[redis]
url = redis://localhost:6379/0

[logging]
level = DEBUG
rotation = 1 day
retention = 30 days

[cors]
origins = http://localhost:3000,http://localhost:8080,http://127.0.0.1:3000
credentials = true
methods = GET,POST,PUT,DELETE,OPTIONS
headers = *

[upload]
dir = uploads
max_file_size = 10485760
allowed_extensions = jpg,jpeg,png,gif,pdf

[minio]
endpoint = localhost:9000
access_key = minioadmin
secret_key = minioadmin
secure = false
bucket_name = ocr-images
enabled = true
