"""
MinIO 客户端工具模块
提供 MinIO 对象存储的封装功能
"""
import uuid
import asyncio
from datetime import datetime, timedelta
from typing import Optional, Tuple
from pathlib import Path
from io import BytesIO

from minio import Minio
from minio.error import S3Error
from fastapi import UploadFile
from PIL import Image

from app.config.settings import settings
from app.core.logging import get_logger

logger = get_logger(__name__)


class MinioClient:
    """MinIO 客户端封装类"""
    
    def __init__(self):
        """初始化 MinIO 客户端"""
        self.endpoint = settings.MINIO_ENDPOINT
        self.access_key = settings.MINIO_ACCESS_KEY
        self.secret_key = settings.MINIO_SECRET_KEY
        self.secure = settings.MINIO_SECURE
        self.bucket_name = settings.MINIO_BUCKET_NAME
        self.enabled = settings.ENABLE_MINIO_STORAGE
        
        if self.enabled:
            try:
                logger.info(f"正在初始化 MinIO 客户端...")
                logger.info(f"MinIO 配置 - 端点: {self.endpoint}")
                logger.info(f"MinIO 配置 - 访问密钥: {self.access_key[:8]}...")
                logger.info(f"MinIO 配置 - 安全连接: {self.secure}")
                logger.info(f"MinIO 配置 - 存储桶: {self.bucket_name}")
                
                self.client = Minio(
                    endpoint=self.endpoint,
                    access_key=self.access_key,
                    secret_key=self.secret_key,
                    secure=self.secure
                )
                logger.info(f"MinIO 客户端初始化成功: {self.endpoint}")
            except Exception as e:
                logger.error(f"MinIO 客户端初始化失败: {e}")
                logger.error(f"错误类型: {type(e).__name__}")
                logger.error(f"错误详情: {str(e)}")
                self.client = None
        else:
            self.client = None
            logger.info("MinIO 存储已禁用")
    
    async def ensure_bucket_exists(self) -> bool:
        """确保存储桶存在"""
        if not self.client:
            return False
        
        try:
            logger.info(f"正在检查存储桶是否存在: {self.bucket_name}")
            
            # 在线程池中运行同步操作
            loop = asyncio.get_event_loop()
            
            # 检查存储桶是否存在
            bucket_exists = await loop.run_in_executor(
                None, 
                self.client.bucket_exists, 
                self.bucket_name
            )
            
            logger.info(f"存储桶 {self.bucket_name} 存在状态: {bucket_exists}")
            
            if not bucket_exists:
                logger.info(f"存储桶不存在，正在创建: {self.bucket_name}")
                # 创建存储桶
                await loop.run_in_executor(
                    None,
                    self.client.make_bucket,
                    self.bucket_name
                )
                logger.info(f"创建存储桶成功: {self.bucket_name}")
            else:
                logger.info(f"存储桶已存在: {self.bucket_name}")
            
            return True
            
        except S3Error as e:
            logger.error(f"存储桶操作失败 (S3Error): {e}")
            logger.error(f"S3错误代码: {e.code}")
            logger.error(f"S3错误消息: {e.message}")
            return False
        except Exception as e:
            logger.error(f"存储桶检查失败: {e}")
            logger.error(f"错误类型: {type(e).__name__}")
            logger.error(f"错误详情: {str(e)}")
            return False
    
    async def upload_file(
        self, 
        file: UploadFile, 
        folder: str = "temp"
    ) -> Optional[Tuple[str, str, str]]:
        """
        上传文件到 MinIO
        
        Args:
            file: 上传的文件对象
            folder: 存储文件夹 (company-templates, ocr-tasks, temp)
            
        Returns:
            Optional[Tuple[str, str, str]]: (image_uuid, minio_path, original_filename) 或 None
        """
        if not self.client or not self.enabled:
            logger.warning("MinIO 客户端未启用或未初始化")
            return None
        
        try:
            logger.info(f"开始上传文件: {file.filename} 到文件夹: {folder}")
            
            # 确保存储桶存在
            if not await self.ensure_bucket_exists():
                logger.error("存储桶创建或检查失败")
                return None
            
            # 生成唯一的图片 UUID
            image_uuid = str(uuid.uuid4())
            logger.info(f"生成图片 UUID: {image_uuid}")
            
            # 获取文件扩展名
            file_extension = Path(file.filename).suffix.lower()
            logger.info(f"文件扩展名: {file_extension}")
            
            # 构建 MinIO 路径
            minio_path = f"{folder}/{image_uuid}_{file.filename}"
            logger.info(f"MinIO 存储路径: {minio_path}")
            
            # 重置文件指针
            await file.seek(0)
            file_content = await file.read()
            logger.info(f"文件大小: {len(file_content)} 字节")
            logger.info(f"文件类型: {file.content_type}")
            
            # 创建文件流
            file_stream = BytesIO(file_content)
            
            # 在线程池中执行上传
            loop = asyncio.get_event_loop()
            
            # 重试机制
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    logger.info(f"开始上传尝试 {attempt + 1}/{max_retries}")
                    
                    await loop.run_in_executor(
                        None,
                        self.client.put_object,
                        self.bucket_name,
                        minio_path,
                        file_stream,
                        len(file_content),
                        file.content_type
                    )
                    
                    logger.info(f"文件上传成功: {minio_path}")
                    return image_uuid, minio_path, file.filename
                    
                except Exception as e:
                    logger.error(f"上传尝试 {attempt + 1} 失败: {e}")
                    logger.error(f"错误类型: {type(e).__name__}")
                    
                    if attempt < max_retries - 1:
                        wait_time = 2 ** attempt  # 1, 2, 4 秒
                        logger.warning(f"上传失败，{wait_time}秒后重试")
                        await asyncio.sleep(wait_time)
                        file_stream.seek(0)  # 重置流位置
                    else:
                        logger.error("所有重试尝试均失败")
                        raise e
            
        except Exception as e:
            logger.error(f"文件上传失败: {e}")
            logger.error(f"错误类型: {type(e).__name__}")
            logger.error(f"错误详情: {str(e)}")
            return None
    
    async def generate_presigned_url(
        self, 
        minio_path: str, 
        expires: timedelta = timedelta(days=7)
    ) -> Optional[str]:
        """
        生成预签名访问 URL
        
        Args:
            minio_path: MinIO 中的文件路径
            expires: URL 过期时间
            
        Returns:
            Optional[str]: 预签名 URL 或 None
        """
        if not self.client or not self.enabled:
            logger.warning("MinIO 客户端未启用或未初始化")
            return None
        
        try:
            logger.info(f"开始生成预签名 URL: {minio_path}")
            logger.info(f"URL 过期时间: {expires}")
            
            loop = asyncio.get_event_loop()
            
            url = await loop.run_in_executor(
                None,
                self.client.presigned_get_object,
                self.bucket_name,
                minio_path,
                expires
            )
            
            logger.info(f"生成预签名 URL 成功: {minio_path}")
            logger.info(f"生成的 URL: {url[:100]}...")  # 只显示前100个字符
            return url
            
        except Exception as e:
            logger.error(f"生成预签名 URL 失败: {e}")
            logger.error(f"错误类型: {type(e).__name__}")
            logger.error(f"错误详情: {str(e)}")
            return None
    
    async def delete_file(self, minio_path: str) -> bool:
        """
        删除 MinIO 中的文件
        
        Args:
            minio_path: MinIO 中的文件路径
            
        Returns:
            bool: 删除是否成功
        """
        if not self.client or not self.enabled:
            logger.warning("MinIO 客户端未启用或未初始化")
            return False
        
        try:
            logger.info(f"开始删除文件: {minio_path}")
            
            loop = asyncio.get_event_loop()
            
            await loop.run_in_executor(
                None,
                self.client.remove_object,
                self.bucket_name,
                minio_path
            )
            
            logger.info(f"文件删除成功: {minio_path}")
            return True
            
        except Exception as e:
            logger.error(f"文件删除失败: {e}")
            logger.error(f"错误类型: {type(e).__name__}")
            logger.error(f"错误详情: {str(e)}")
            return False
    
    async def get_object_stream(self, object_name: str):
        """
        获取对象的流式数据
        
        Args:
            object_name: MinIO 中的对象路径
            
        Returns:
            HTTPResponse: MinIO 对象的响应流，如果失败则返回 None
        """
        if not self.client or not self.enabled:
            logger.warning("MinIO 客户端未启用或未初始化")
            return None
        
        try:
            logger.info(f"开始获取对象流: {object_name}")
            
            loop = asyncio.get_event_loop()
            
            response = await loop.run_in_executor(
                None,
                self.client.get_object,
                self.bucket_name,
                object_name
            )
            
            logger.info(f"获取对象流成功: {object_name}")
            return response
            
        except S3Error as e:
            if e.code == 'NoSuchKey':
                logger.warning(f"对象不存在: {object_name}")
            else:
                logger.error(f"获取对象流失败 (S3Error): {e}")
                logger.error(f"S3错误代码: {e.code}")
                logger.error(f"S3错误消息: {e.message}")
            return None
        except Exception as e:
            logger.error(f"获取对象流失败: {e}")
            logger.error(f"错误类型: {type(e).__name__}")
            logger.error(f"错误详情: {str(e)}")
            return None
    
    async def get_object_info(self, object_name: str):
        """
        获取对象的元数据信息
        
        Args:
            object_name: MinIO 中的对象路径
            
        Returns:
            Object: MinIO 对象的元数据信息，如果失败则返回 None
        """
        if not self.client or not self.enabled:
            logger.warning("MinIO 客户端未启用或未初始化")
            return None
        
        try:
            logger.info(f"开始获取对象信息: {object_name}")
            
            loop = asyncio.get_event_loop()
            
            stat = await loop.run_in_executor(
                None,
                self.client.stat_object,
                self.bucket_name,
                object_name
            )
            
            logger.info(f"获取对象信息成功: {object_name}")
            logger.info(f"对象大小: {stat.size} 字节")
            logger.info(f"对象类型: {stat.content_type}")
            logger.info(f"最后修改时间: {stat.last_modified}")
            
            return stat
            
        except S3Error as e:
            if e.code == 'NoSuchKey':
                logger.warning(f"对象不存在: {object_name}")
            else:
                logger.error(f"获取对象信息失败 (S3Error): {e}")
                logger.error(f"S3错误代码: {e.code}")
                logger.error(f"S3错误消息: {e.message}")
            return None
        except Exception as e:
            logger.error(f"获取对象信息失败: {e}")
            logger.error(f"错误类型: {type(e).__name__}")
            logger.error(f"错误详情: {str(e)}")
            return None
    
    async def get_object_data(self, object_name: str) -> Optional[Tuple[bytes, str, int]]:
        """
        获取对象的二进制数据
        
        Args:
            object_name: MinIO 中的对象路径
            
        Returns:
            Optional[Tuple[bytes, str, int]]: (文件数据, 内容类型, 文件大小) 或 None
        """
        if not self.client or not self.enabled:
            logger.warning("MinIO 客户端未启用或未初始化")
            return None
        
        try:
            logger.info(f"开始获取对象数据: {object_name}")
            
            loop = asyncio.get_event_loop()
            
            # 先获取对象信息
            stat = await loop.run_in_executor(
                None,
                self.client.stat_object,
                self.bucket_name,
                object_name
            )
            
            # 获取对象数据流
            response = await loop.run_in_executor(
                None,
                self.client.get_object,
                self.bucket_name,
                object_name
            )
            
            # 读取所有数据
            data = response.read()
            response.close()
            response.release_conn()
            
            logger.info(f"获取对象数据成功: {object_name}")
            logger.info(f"数据大小: {len(data)} 字节")
            logger.info(f"内容类型: {stat.content_type}")
            
            return data, stat.content_type, len(data)
            
        except S3Error as e:
            if e.code == 'NoSuchKey':
                logger.warning(f"对象不存在: {object_name}")
            else:
                logger.error(f"获取对象数据失败 (S3Error): {e}")
                logger.error(f"S3错误代码: {e.code}")
                logger.error(f"S3错误消息: {e.message}")
            return None
        except Exception as e:
            logger.error(f"获取对象数据失败: {e}")
            logger.error(f"错误类型: {type(e).__name__}")
            logger.error(f"错误详情: {str(e)}")
            return None


# 全局 MinIO 客户端实例 - 使用懒加载避免重复初始化
_minio_client = None

def get_minio_client() -> MinioClient:
    """获取MinIO客户端实例（单例模式）"""
    global _minio_client
    if _minio_client is None:
        _minio_client = MinioClient()
    return _minio_client

# 保持向后兼容 - 使用属性访问而不是立即初始化
class _MinioClientProxy:
    """MinIO客户端代理，实现懒加载"""
    def __getattr__(self, name):
        client = get_minio_client()
        return getattr(client, name)

minio_client = _MinioClientProxy()


async def upload_image_to_minio(
    file: UploadFile, 
    folder: str = "temp"
) -> Optional[Tuple[str, str, str]]:
    """
    上传图片到 MinIO 的便捷函数
    
    Args:
        file: 上传的文件对象
        folder: 存储文件夹
        
    Returns:
        Optional[Tuple[str, str, str]]: (image_uuid, minio_path, original_filename) 或 None
    """
    client = get_minio_client()
    return await client.upload_file(file, folder)


async def get_image_url(minio_path: str) -> Optional[str]:
    """
    获取图片访问 URL 的便捷函数
    
    Args:
        minio_path: MinIO 中的文件路径
        
    Returns:
        Optional[str]: 预签名 URL 或 None
    """
    client = get_minio_client()
    return await client.generate_presigned_url(minio_path)


async def get_image_data(minio_path: str) -> Optional[Tuple[bytes, str, int]]:
    """
    获取图片二进制数据的便捷函数
    
    Args:
        minio_path: MinIO 中的文件路径
        
    Returns:
        Optional[Tuple[bytes, str, int]]: (文件数据, 内容类型, 文件大小) 或 None
    """
    client = get_minio_client()
    return await client.get_object_data(minio_path) 