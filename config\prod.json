{"app": {"name": "FastAPI OCR Project", "version": "1.0.0", "debug": false, "host": "0.0.0.0", "port": 8000}, "database": {"url": "mysql+aiomysql://root:QWEasdZXC456456@***********:3306/ocr_data", "echo": false, "pool_size": 20, "max_overflow": 30}, "redis": {"url": "redis://localhost:6379/0"}, "logging": {"level": "INFO", "rotation": "1 day", "retention": "30 days"}, "cors": {"origins": ["https://yourdomain.com"], "credentials": true, "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "headers": ["*"]}, "upload": {"dir": "uploads", "max_file_size": 10485760, "allowed_extensions": ["jpg", "jpeg", "png", "gif", "pdf"]}, "minio": {"endpoint": "***********:9000", "access_key": "u7Kar8QUvbWwnKBQvUJ8", "secret_key": "X6qACTkEMsm9HVrsXx4SwBLBzYo05xDQoSFzSqAO", "secure": false, "bucket_name": "ocr", "enabled": true}}