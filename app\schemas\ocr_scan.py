"""
OCR扫描数据验证模型
定义OCR扫描相关的请求和响应数据结构
"""
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field, validator
from datetime import datetime


class TextLineResult(BaseModel):
    """OCR识别的文本行结果"""
    text: str = Field(..., description="识别的文本内容")
    confidence: float = Field(..., ge=0.0, le=1.0, description="识别置信度")
    bbox: List[int] = Field(..., description="文本边界框坐标 [x, y, width, height]")
    
    @validator('bbox')
    def validate_bbox(cls, v):
        """验证边界框格式"""
        if len(v) != 4:
            raise ValueError('边界框必须包含4个坐标值 [x, y, width, height]')
        if any(coord < 0 for coord in v):
            raise ValueError('边界框坐标不能为负数')
        return v


class OCRResult(BaseModel):
    """OCR识别完整结果"""
    text_lines: List[TextLineResult] = Field(..., description="识别的文本行列表")
    processing_time: float = Field(..., description="处理时间（秒）")
    image_size: List[int] = Field(..., description="图片尺寸 [width, height]")


class CompanyMatchResult(BaseModel):
    """公司匹配结果"""
    company_id: int = Field(..., description="公司模版ID")
    company_name: str = Field(..., description="匹配的公司名称")
    matched_text: str = Field(..., description="OCR中匹配的文本")
    confidence: float = Field(..., ge=0.0, le=1.0, description="匹配置信度")
    text_position: List[int] = Field(..., description="匹配文本的位置信息")


class FieldMatchResult(BaseModel):
    """字段匹配结果"""
    field_name: str = Field(..., description="字段名称")
    matched_text: str = Field(..., description="匹配的文本内容")
    confidence: float = Field(..., ge=0.0, le=1.0, description="匹配置信度")
    text_position: List[int] = Field(..., description="匹配文本的位置信息")


class OCRMatchResponse(BaseModel):
    """OCR匹配响应结果（简化版，只返回匹配信息）"""
    company_name: str = Field(..., description="匹配的公司名称")
    table_data: List[Dict[str, Any]] = Field(..., description="提取的表格数据")
    table_rows: int = Field(..., description="表格行数")
    field_names: List[str] = Field(..., description="字段名称列表")
    processing_time: float = Field(..., description="处理时间（秒）")
    confidence_threshold: float = Field(..., description="置信度阈值")
    content_height: int = Field(..., description="内容高度")
    image_uuid: Optional[str] = Field(None, description="图片UUID")
    image_filename: Optional[str] = Field(None, description="图片文件名")
    image_minio_path: Optional[str] = Field(None, description="图片MinIO路径")
    image_upload_time: Optional[str] = Field(None, description="图片上传时间")
    id: Optional[int] = Field(None, description="OCR任务ID")


class OCRTableDataUpdateRequest(BaseModel):
    """OCR表格数据更新请求"""
    id: int = Field(..., description="OCR扫描记录ID")
    table_data: List[Dict[str, Any]] = Field(..., description="更新的表格数据")
    
    @validator('id')
    def validate_id(cls, v):
        """验证任务ID"""
        if v <= 0:
            raise ValueError('任务ID必须是正整数')
        return v
    
    @validator('table_data')
    def validate_table_data(cls, v):
        """验证表格数据格式"""
        if not v:
            raise ValueError('表格数据不能为空')
        
        for row in v:
            if not isinstance(row, dict):
                raise ValueError('表格数据每行必须是字典格式')
            if 'row_index' not in row:
                raise ValueError('表格数据每行必须包含 row_index 字段')
            if not isinstance(row['row_index'], int) or row['row_index'] < 0:
                raise ValueError('row_index 必须是非负整数')
        
        return v


class OCRAnalysisResponse(BaseModel):
    """OCR分析响应结果（完整版）"""
    ocr_result: OCRResult = Field(..., description="OCR识别结果")
    company_match: Optional[CompanyMatchResult] = Field(
        None, 
        description="公司匹配结果，如果未找到匹配则为null"
    )
    field_matches: List[FieldMatchResult] = Field(
        default_factory=list, 
        description="字段匹配结果列表"
    )
    company_template: Optional[Dict[str, Any]] = Field(
        None, 
        description="匹配的公司模版信息"
    )
    analysis_summary: Dict[str, Any] = Field(
        ..., 
        description="分析摘要信息"
    ) 