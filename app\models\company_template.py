"""
公司模版数据库模型
用于存储公司单据模版的相关信息
"""
from typing import Optional, List, Dict, Any
from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, JSON, DateTime
from sqlalchemy.orm import validates

from .base import BaseModel


class CompanyTemplate(BaseModel):
    """
    公司模版数据模型
    
    存储公司单据模版信息，包括：
    - 公司基本信息
    - 单据图片
    - 字段提取信息
    - 标注信息（包含padding信息）
    """
    
    __tablename__ = "company_templates"
    
    # 公司基本信息
    company_name = Column(
        String(200), 
        nullable=False, 
        index=True,
        comment="公司名称"
    )
    
    # 单据图片路径
    document_image = Column(
        String(500), 
        nullable=False,
        comment="公司单据图片路径或URL"
    )
    
    # 提取字段信息
    extract_fields = Column(
        JSON, 
        nullable=False,
        comment="提取表头字段数组"
    )
    
    # 内容高度
    content_height = Column(
        Integer, 
        nullable=False,
        comment="内容高度"
    )
    
    # 标注信息（包含padding信息）
    annotations = Column(
        JSON, 
        nullable=False,
        comment="标注信息，JSON格式存储，包含padding信息"
    )
    
    # 图片相关字段
    image_uuid = Column(
        String(36),
        nullable=True,
        comment="图片UUID"
    )
    
    image_filename = Column(
        String(255),
        nullable=True,
        comment="原始文件名"
    )
    
    image_minio_path = Column(
        String(500),
        nullable=True,
        comment="MinIO存储路径"
    )
    
    image_upload_time = Column(
        DateTime,
        nullable=True,
        comment="图片上传时间"
    )
    
    @validates('company_name')
    def validate_company_name(self, key, value):
        """验证公司名称"""
        if not value or not value.strip():
            raise ValueError("公司名称不能为空")
        if len(value.strip()) > 200:
            raise ValueError("公司名称长度不能超过200个字符")
        return value.strip()
    
    @validates('document_image')
    def validate_document_image(self, key, value):
        """验证单据图片路径"""
        if not value or not value.strip():
            raise ValueError("单据图片路径不能为空")
        return value.strip()
    
    @validates('content_height')
    def validate_integer_fields(self, key, value):
        """验证整数字段"""
        if value is None:
            raise ValueError(f"{key}不能为空")
        if not isinstance(value, int):
            raise ValueError(f"{key}必须是整数")
        return value
    
    @validates('extract_fields', 'annotations')
    def validate_json_fields(self, key, value):
        """验证JSON字段"""
        if value is None:
            raise ValueError(f"{key}不能为空")
        return value
    
    def __repr__(self):
        """字符串表示"""
        return f"<CompanyTemplate(id={self.id}, company_name='{self.company_name}')>"
    
    def to_dict_summary(self) -> Dict[str, Any]:
        """
        转换为摘要字典（用于列表显示）
        
        Returns:
            dict: 包含基本信息的字典
        """
        return {
            "id": self.id,
            "company_name": self.company_name,
            "document_image": self.document_image,
            "extract_fields": self.extract_fields,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "image_uuid": self.image_uuid,
            "image_filename": self.image_filename,
            "image_minio_path": self.image_minio_path,
            "image_upload_time": self.image_upload_time.isoformat() if self.image_upload_time else None,
        }
    
    def to_dict_detail(self) -> Dict[str, Any]:
        """
        转换为详细字典（用于详情显示）
        
        Returns:
            dict: 包含所有信息的字典
        """
        return {
            "id": self.id,
            "company_name": self.company_name,
            "document_image": self.document_image,
            "extract_fields": self.extract_fields,
            "content_height": self.content_height,
            "annotations": self.annotations,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "is_deleted": self.is_deleted,
            "image_uuid": self.image_uuid,
            "image_filename": self.image_filename,
            "image_minio_path": self.image_minio_path,
            "image_upload_time": self.image_upload_time.isoformat() if self.image_upload_time else None,
        }
    
    @classmethod
    def create_from_dict(cls, data: Dict[str, Any]) -> 'CompanyTemplate':
        """
        从字典创建公司模版实例
        
        Args:
            data: 包含公司模版信息的字典
            
        Returns:
            CompanyTemplate: 公司模版实例
        """
        return cls(
            company_name=data.get('company_name'),
            document_image=data.get('document_image'),
            extract_fields=data.get('extract_fields', []),
            content_height=data.get('content_height', 0),
            annotations=data.get('annotations', []),
            image_uuid=data.get('image_uuid'),
            image_filename=data.get('image_filename'),
            image_minio_path=data.get('image_minio_path'),
            image_upload_time=data.get('image_upload_time')
        )
    
    def update_from_dict(self, data: Dict[str, Any]) -> None:
        """
        从字典更新公司模版信息
        
        Args:
            data: 包含更新信息的字典
        """
        updateable_fields = [
            'company_name', 'document_image', 'extract_fields',
            'content_height', 'annotations', 'image_uuid', 
            'image_filename', 'image_minio_path', 'image_upload_time'
        ]
        
        for field in updateable_fields:
            if field in data and data[field] is not None:
                setattr(self, field, data[field]) 