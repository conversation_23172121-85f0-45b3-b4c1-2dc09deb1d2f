"""
OCR处理核心工具模块
提供图片预处理、OCR识别等功能
"""
import time
import asyncio
import threading
from typing import List, Dict, Any, Optional, Tuple
from PIL import Image
from concurrent.futures import ThreadPoolExecutor

from surya.recognition import RecognitionPredictor
from surya.detection import DetectionPredictor
from app.core.logging import get_logger

logger = get_logger(__name__)

# 全局OCR预测器实例（避免重复初始化）
_recognition_predictor: Optional[RecognitionPredictor] = None
_detection_predictor: Optional[DetectionPredictor] = None
# 线程锁保护OCR预测器的并发访问
_ocr_lock = threading.Lock()


def get_ocr_predictors() -> <PERSON><PERSON>[RecognitionPredictor, DetectionPredictor]:
    """
    获取OCR预测器实例（单例模式，线程安全）
    
    Returns:
        Tu<PERSON>[RecognitionPredictor, DetectionPredictor]: OCR预测器实例
    """
    global _recognition_predictor, _detection_predictor
    
    with _ocr_lock:
        if _recognition_predictor is None or _detection_predictor is None:
            logger.info("初始化OCR预测器...")
            _recognition_predictor = RecognitionPredictor()
            _detection_predictor = DetectionPredictor()
            logger.info("OCR预测器初始化完成")
    
    return _recognition_predictor, _detection_predictor


def preprocess_image(image: Image.Image, max_size: int = 2048) -> Image.Image:
    """
    图片预处理
    
    Args:
        image: 原始图片
        max_size: 最大尺寸限制
        
    Returns:
        Image.Image: 预处理后的图片
    """
    try:
        # 转换为RGB模式
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # 调整图片大小（保持宽高比）
        width, height = image.size
        if max(width, height) > max_size:
            ratio = max_size / max(width, height)
            new_width = int(width * ratio)
            new_height = int(height * ratio)
            image = image.resize((new_width, new_height), Image.LANCZOS)
            logger.info(f"图片大小调整: {width}x{height} -> {new_width}x{new_height}")
        
        return image
    except Exception as e:
        logger.error(f"图片预处理失败: {e}")
        raise


def run_ocr_sync(image: Image.Image) -> List[Any]:
    """
    同步执行OCR识别（在线程池中运行，线程安全）
    
    Args:
        image: 预处理后的图片
        
    Returns:
        List[Any]: OCR识别结果
    """
    try:
        recognition_predictor, detection_predictor = get_ocr_predictors()
        
        # 使用锁保护OCR预测器的并发访问
        with _ocr_lock:
            # 执行OCR识别
            predictions = recognition_predictor(
                [image], 
                det_predictor=detection_predictor
            )
        
        return predictions
    except Exception as e:
        logger.error(f"OCR识别失败: {e}")
        raise


async def perform_ocr_recognition(image: Image.Image) -> Any:
    """
    执行OCR识别（异步）
    
    Args:
        image: 图片对象
        
    Returns:
        Any: OCR识别结果的image_result对象
        
    Raises:
        Exception: OCR识别失败
    """
    try:
        start_time = time.time()
        
        # 图片预处理
        processed_image = preprocess_image(image)
        
        # 在线程池中执行OCR识别
        loop = asyncio.get_event_loop()
        with ThreadPoolExecutor(max_workers=1) as executor:
            predictions = await loop.run_in_executor(
                executor, 
                run_ocr_sync, 
                processed_image
            )
        
        processing_time = time.time() - start_time
        
        # 直接返回predictions的第一个image_result
        if predictions and len(predictions) > 0:
            image_result = predictions[0]
            logger.info(f"OCR识别完成，耗时 {processing_time:.2f}秒")
            return image_result
        else:
            logger.warning("OCR识别返回空结果")
            return None
        
    except Exception as e:
        logger.error(f"OCR识别过程中发生错误: {e}")
        raise Exception(f"OCR识别失败: {str(e)}")


def validate_ocr_result(image_result: Any) -> bool:
    """
    验证OCR结果是否有效
    
    Args:
        image_result: OCR识别结果的image_result对象
        
    Returns:
        bool: 是否有效
    """
    if not image_result:
        return False
    
    # 检查是否有text_lines属性且数组长度大于0
    if hasattr(image_result, 'text_lines') and image_result.text_lines:
        return len(image_result.text_lines) > 0
    
    return False 