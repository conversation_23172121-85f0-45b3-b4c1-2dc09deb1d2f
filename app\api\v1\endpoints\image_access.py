"""
图片访问端点
提供图片访问相关的API接口
"""
import mimetypes
from pathlib import Path as PathLib
from urllib.parse import quote
from fastapi import APIRouter, HTTPException, Depends, Path, Query
from fastapi.responses import Response
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_

from app.config.database import get_db
from app.models.company_template import CompanyTemplate
from app.utils.minio_client import get_image_url, get_image_data
from app.schemas.response import ApiResponse, StatusCode, ResponseMessage
from app.core.logging import get_logger

logger = get_logger(__name__)

router = APIRouter()


def get_media_type_from_filename(filename: str) -> str:
    """
    根据文件名推断媒体类型
    
    Args:
        filename: 文件名
        
    Returns:
        str: 媒体类型
    """
    if not filename:
        return "application/octet-stream"
    
    # 获取文件扩展名
    file_path = PathLib(filename)
    extension = file_path.suffix.lower()
    
    # 图片类型映射
    image_types = {
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.png': 'image/png',
        '.gif': 'image/gif',
        '.bmp': 'image/bmp',
        '.webp': 'image/webp',
        '.tiff': 'image/tiff',
        '.tif': 'image/tiff',
        '.svg': 'image/svg+xml',
        '.ico': 'image/x-icon'
    }
    
    # 优先使用自定义映射
    if extension in image_types:
        return image_types[extension]
    
    # 使用系统的mimetypes模块
    mime_type, _ = mimetypes.guess_type(filename)
    if mime_type:
        return mime_type
    
    # 默认返回二进制流
    return "application/octet-stream"


@router.get(
    "/url/{image_uuid}",
    response_model=ApiResponse[dict],
    summary="获取图片访问URL",
    description="根据图片UUID获取预签名访问URL"
)
async def get_image_access_url(
    image_uuid: str = Path(..., description="图片UUID"),
    db: AsyncSession = Depends(get_db)
):
    """
    获取图片访问URL
    
    Args:
        image_uuid: 图片UUID
        db: 数据库会话
        
    Returns:
        ApiResponse: 包含预签名URL的响应
    """
    try:
        # 查询图片信息
        query = select(CompanyTemplate).where(
            and_(
                CompanyTemplate.image_uuid == image_uuid,
                CompanyTemplate.is_deleted == False
            )
        )
        
        result = await db.execute(query)
        template = result.scalar_one_or_none()
        
        if not template:
            logger.warning(f"未找到图片记录: {image_uuid}")
            raise HTTPException(
                status_code=StatusCode.NOT_FOUND,
                detail="图片记录不存在"
            )
        
        if not template.image_minio_path:
            logger.warning(f"图片记录缺少MinIO路径: {image_uuid}")
            raise HTTPException(
                status_code=StatusCode.NOT_FOUND,
                detail="图片文件不存在"
            )
        
        # 生成预签名URL
        presigned_url = await get_image_url(template.image_minio_path)
        
        if not presigned_url:
            logger.error(f"生成预签名URL失败: {template.image_minio_path}")
            raise HTTPException(
                status_code=StatusCode.INTERNAL_SERVER_ERROR,
                detail="生成图片访问URL失败"
            )
        
        response_data = {
            "image_uuid": image_uuid,
            "image_filename": template.image_filename,
            "presigned_url": presigned_url,
            "company_name": template.company_name
        }
        
        logger.info(f"生成图片访问URL成功: {image_uuid}")
        
        return ApiResponse(
            code=StatusCode.SUCCESS,
            message=ResponseMessage.SUCCESS,
            data=response_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取图片访问URL失败: {e}")
        raise HTTPException(
            status_code=StatusCode.INTERNAL_SERVER_ERROR,
            detail=f"获取图片访问URL失败: {str(e)}"
        )


@router.get(
    "/info/{image_uuid}",
    response_model=ApiResponse[dict],
    summary="获取图片信息",
    description="根据图片UUID获取图片基本信息"
)
async def get_image_info(
    image_uuid: str = Path(..., description="图片UUID"),
    db: AsyncSession = Depends(get_db)
):
    """
    获取图片信息
    
    Args:
        image_uuid: 图片UUID
        db: 数据库会话
        
    Returns:
        ApiResponse: 包含图片信息的响应
    """
    try:
        # 查询图片信息
        query = select(CompanyTemplate).where(
            and_(
                CompanyTemplate.image_uuid == image_uuid,
                CompanyTemplate.is_deleted == False
            )
        )
        
        result = await db.execute(query)
        template = result.scalar_one_or_none()
        
        if not template:
            logger.warning(f"未找到图片记录: {image_uuid}")
            raise HTTPException(
                status_code=StatusCode.NOT_FOUND,
                detail="图片记录不存在"
            )
        
        response_data = {
            "image_uuid": template.image_uuid,
            "image_filename": template.image_filename,
            "image_minio_path": template.image_minio_path,
            "image_upload_time": template.image_upload_time.isoformat() if template.image_upload_time else None,
            "company_name": template.company_name,
            "company_id": template.id
        }
        
        logger.info(f"获取图片信息成功: {image_uuid}")
        
        return ApiResponse(
            code=StatusCode.SUCCESS,
            message=ResponseMessage.SUCCESS,
            data=response_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取图片信息失败: {e}")
        raise HTTPException(
            status_code=StatusCode.INTERNAL_SERVER_ERROR,
            detail=f"获取图片信息失败: {str(e)}"
        )


@router.get(
    "/binary/{image_uuid}",
    summary="获取图片二进制数据",
    description="根据图片UUID获取图片的二进制数据，直接返回文件内容"
)
async def get_image_binary_by_uuid(
    image_uuid: str = Path(..., description="图片UUID"),
    db: AsyncSession = Depends(get_db)
):
    """
    通过UUID获取图片二进制数据
    
    Args:
        image_uuid: 图片UUID
        db: 数据库会话
        
    Returns:
        Response: 图片的二进制数据响应
    """
    try:
        # 查询图片信息
        query = select(CompanyTemplate).where(
            and_(
                CompanyTemplate.image_uuid == image_uuid,
                CompanyTemplate.is_deleted == False
            )
        )
        
        result = await db.execute(query)
        template = result.scalar_one_or_none()
        
        if not template:
            logger.warning(f"未找到图片记录: {image_uuid}")
            raise HTTPException(
                status_code=StatusCode.NOT_FOUND,
                detail="图片记录不存在"
            )
        
        if not template.image_minio_path:
            logger.warning(f"图片记录缺少MinIO路径: {image_uuid}")
            raise HTTPException(
                status_code=StatusCode.NOT_FOUND,
                detail="图片文件不存在"
            )
        
        # 从MinIO获取图片二进制数据
        image_data_result = await get_image_data(template.image_minio_path)
        
        if not image_data_result:
            logger.error(f"从MinIO获取图片数据失败: {template.image_minio_path}")
            raise HTTPException(
                status_code=StatusCode.NOT_FOUND,
                detail="图片文件不存在或无法访问"
            )
        
        file_data, content_type, file_size = image_data_result
        
        logger.info(f"成功获取图片二进制数据: {image_uuid}, 大小: {file_size} 字节")
        
        # 确定正确的媒体类型
        # 优先使用MinIO返回的content_type，如果不可靠则根据文件名推断
        if not content_type or content_type == "application/octet-stream":
            content_type = get_media_type_from_filename(template.image_filename)
        
        logger.info(f"图片媒体类型: {content_type}")
        
        # 设置响应头
        headers = {
            "Content-Length": str(file_size),
            "Content-Disposition": f'inline; filename*=UTF-8\'\'{quote(template.image_filename)}',
            "Cache-Control": "public, max-age=3600"  # 缓存1小时
        }
        
        return Response(
            content=file_data,
            media_type=content_type,
            headers=headers
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取图片二进制数据失败: {e}")
        raise HTTPException(
            status_code=StatusCode.INTERNAL_SERVER_ERROR,
            detail=f"获取图片二进制数据失败: {str(e)}"
        )


@router.get(
    "/binary/object/{object_name:path}",
    summary="通过对象名获取文件二进制数据",
    description="根据MinIO对象名获取文件的二进制数据，直接返回文件内容"
)
async def get_file_binary_by_object_name(
    object_name: str = Path(..., description="MinIO对象名/路径"),
    download: bool = Query(False, description="是否作为下载文件返回")
):
    """
    通过对象名获取文件二进制数据
    
    Args:
        object_name: MinIO中的对象名/路径
        download: 是否作为下载文件返回
        
    Returns:
        Response: 文件的二进制数据响应
    """
    try:
        logger.info(f"开始获取对象二进制数据: {object_name}")
        
        # 从MinIO获取文件二进制数据
        file_data_result = await get_image_data(object_name)
        
        if not file_data_result:
            logger.warning(f"对象不存在或无法访问: {object_name}")
            raise HTTPException(
                status_code=StatusCode.NOT_FOUND,
                detail="文件不存在或无法访问"
            )
        
        file_data, content_type, file_size = file_data_result
        
        logger.info(f"成功获取对象二进制数据: {object_name}, 大小: {file_size} 字节")
        
        # 从对象名中提取文件名
        filename = object_name.split('/')[-1] if '/' in object_name else object_name
        
        # 确定正确的媒体类型
        # 优先使用MinIO返回的content_type，如果不可靠则根据文件名推断
        if not content_type or content_type == "application/octet-stream":
            content_type = get_media_type_from_filename(filename)
        
        logger.info(f"文件媒体类型: {content_type}")
        
        # 设置响应头
        headers = {
            "Content-Length": str(file_size),
            "Cache-Control": "public, max-age=3600"  # 缓存1小时
        }
        
        # 根据download参数设置Content-Disposition
        if download:
            headers["Content-Disposition"] = f'attachment; filename*=UTF-8\'\'{quote(filename)}'
        else:
            headers["Content-Disposition"] = f'inline; filename*=UTF-8\'\'{quote(filename)}'
        
        return Response(
            content=file_data,
            media_type=content_type,
            headers=headers
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取对象二进制数据失败: {e}")
        raise HTTPException(
            status_code=StatusCode.INTERNAL_SERVER_ERROR,
            detail=f"获取对象二进制数据失败: {str(e)}"
        ) 